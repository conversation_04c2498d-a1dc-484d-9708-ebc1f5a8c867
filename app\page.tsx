"use client"

import { useState, useEffect } from "react"
import { ConsultorList } from "@/components/consultor-list"
import { ChatInterface } from "@/components/chat-interface"

export type Consultor = {
  id: string
  name: string
  specialty: string
  avatar: string
  description: string
  systemPrompt: string
}

export const consultores: Consultor[] = [
  {
    id: "juridico",
    name: "Dr. <PERSON>",
    specialty: "Consultor Jurídico",
    avatar: "/professional-lawyer-with-suit-and-tie.png",
    description: "Especialista em direito empresarial, trabalhista e tributário",
    systemPrompt:
      "Você é um consultor jurídico experiente especializado em direito empresarial, trabalhista e tributário. Forneça orientações legais precisas e práticas, sempre mencionando quando é necessário consultar documentação específica ou buscar assessoria presencial.",
  },
  {
    id: "contador",
    name: "<PERSON><PERSON><PERSON> <PERSON>",
    specialty: "<PERSON>tadora",
    avatar: "/professional-female-accountant-with-calculator-and.png",
    description: "Especialista em contabilidade empresarial e planejamento financeiro",
    systemPrompt:
      "Você é uma contadora experiente especializada em contabilidade empresarial, planejamento financeiro e gestão fiscal. Ajude com questões contábeis, demonstrações financeiras, fluxo de caixa e estratégias de otimização fiscal.",
  },
  {
    id: "auditor",
    name: "Dr. Roberto Lima",
    specialty: "Auditor Fiscal",
    avatar: "/professional-tax-auditor-with-documents-and-magnif.png",
    description: "Especialista em auditoria fiscal e compliance tributário",
    systemPrompt:
      "Você é um auditor fiscal experiente especializado em compliance tributário, auditoria interna e externa, e conformidade fiscal. Ajude com questões de auditoria, identificação de riscos fiscais e implementação de controles internos.",
  },
]

export default function Home() {
  const [selectedConsultor, setSelectedConsultor] = useState<Consultor | null>(null)
  const [selectedModel, setSelectedModel] = useState("deepseek/deepseek-chat-v3-0324:free")

  useEffect(() => {
    const savedModel = localStorage.getItem("selectedModel")
    if (savedModel) {
      setSelectedModel(savedModel)
    }
  }, [])

  return (
    <div className="h-screen bg-background">
      {selectedConsultor ? (
        <ChatInterface
          consultor={selectedConsultor}
          onBack={() => setSelectedConsultor(null)}
        />
      ) : (
        <ConsultorList
          consultores={consultores}
          onSelectConsultor={setSelectedConsultor}
          onModelChange={setSelectedModel}
        />
      )}
    </div>
  )
}
