"use client"

import { useState, useEffect } from "react"
import { ConsultorList } from "@/components/consultor-list"
import { ChatInterface } from "@/components/chat-interface"

export type Consultor = {
  id: string
  name: string
  specialty: string
  avatar: string
  description: string
  systemPrompt: string
}

export const consultores: Consultor[] = [
  {
    id: "juridico",
    name: "Dr. <PERSON>",
    specialty: "Consultor Jurídico",
    avatar: "/professional-lawyer-with-suit-and-tie.png",
    description: "Especialista em direito empresarial, trabalhista e tributário",
    systemPrompt:
      "Você é um consultor jurídico da Adição Contabilidade, especializado em direito empresarial, trabalhista e tributário. Forneça orientações legais precisas e práticas. Quando necessário para casos complexos ou específicos, ofereça transferir a conversa para um especialista humano da Adição Contabilidade. Sempre se identifique como consultor da empresa.",
  },
  {
    id: "contador",
    name: "<PERSON><PERSON><PERSON> <PERSON>",
    specialty: "Contadora",
    avatar: "/professional-female-accountant-with-calculator-and.png",
    description: "Especialista em contabilidade empresarial e planejamento financeiro",
    systemPrompt:
      "Você é uma contadora da Adição Contabilidade, especializada em contabilidade empresarial, planejamento financeiro e gestão fiscal. Ajude com questões contábeis, demonstrações financeiras, fluxo de caixa e estratégias de otimização fiscal. Para casos que requerem análise detalhada de documentos ou situações específicas, ofereça transferir para um especialista humano da Adição Contabilidade. Sempre se identifique como contadora da empresa.",
  },
  {
    id: "auditor",
    name: "Dr. Roberto Lima",
    specialty: "Auditor Fiscal",
    avatar: "/professional-tax-auditor-with-documents-and-magnif.png",
    description: "Especialista em auditoria fiscal e compliance tributário",
    systemPrompt:
      "Você é um auditor fiscal da Adição Contabilidade, especializado em compliance tributário, auditoria interna e externa, e conformidade fiscal. Ajude com questões de auditoria, identificação de riscos fiscais e implementação de controles internos. Para auditorias complexas ou análises detalhadas, ofereça transferir para um especialista humano da Adição Contabilidade. Sempre se identifique como auditor da empresa.",
  },
]

export default function Home() {
  const [selectedConsultor, setSelectedConsultor] = useState<Consultor | null>(null)
  const [selectedModel, setSelectedModel] = useState("deepseek/deepseek-chat-v3-0324:free")

  useEffect(() => {
    const savedModel = localStorage.getItem("selectedModel")
    if (savedModel) {
      setSelectedModel(savedModel)
    }
  }, [])

  return (
    <div className="h-screen bg-background">
      {selectedConsultor ? (
        <ChatInterface
          consultor={selectedConsultor}
          onBack={() => setSelectedConsultor(null)}
        />
      ) : (
        <ConsultorList
          consultores={consultores}
          onSelectConsultor={setSelectedConsultor}
          onModelChange={setSelectedModel}
        />
      )}
    </div>
  )
}
