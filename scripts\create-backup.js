#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const { execSync } = require('child_process');

class BackupSystem {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.backupDir = path.join(this.projectRoot, 'backups');
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    this.timeString = new Date().toTimeString().split(' ')[0].replace(/:/g, '-');
    this.version = this.getNextVersion();
    this.backupName = `backup_v${this.version}_${this.timestamp}_${this.timeString}`;
    this.backupPath = path.join(this.backupDir, this.backupName);
  }

  getNextVersion() {
    if (!fs.existsSync(this.backupDir)) {
      return '1.0';
    }
    
    const existingBackups = fs.readdirSync(this.backupDir)
      .filter(dir => dir.startsWith('backup_v'))
      .map(dir => {
        const match = dir.match(/backup_v(\d+\.\d+)/);
        return match ? parseFloat(match[1]) : 0;
      })
      .sort((a, b) => b - a);
    
    const latestVersion = existingBackups[0] || 0;
    return (latestVersion + 0.1).toFixed(1);
  }

  createBackupDirectory() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
    fs.mkdirSync(this.backupPath, { recursive: true });
  }

  getFilesToBackup() {
    const includePatterns = [
      'app/**/*',
      'components/**/*',
      'lib/**/*',
      'public/**/*',
      'scripts/**/*',
      'styles/**/*',
      'package.json',
      'package-lock.json',
      'pnpm-lock.yaml',
      'next.config.mjs',
      'tailwind.config.ts',
      'tsconfig.json',
      '.env.example',
      '.env.local',
      '.env.production',
      'README.md',
      '*.md'
    ];

    const excludePatterns = [
      'node_modules',
      '.next',
      '.git',
      'backups',
      '*.log',
      '.DS_Store',
      'Thumbs.db'
    ];

    return { includePatterns, excludePatterns };
  }

  generateManifest() {
    const manifest = {
      backup: {
        version: this.version,
        timestamp: new Date().toISOString(),
        name: this.backupName,
        description: 'Automated backup of Adição Contabilidade Chat System'
      },
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      },
      project: {
        name: 'Consultant Chat App - Adição Contabilidade',
        version: this.getProjectVersion(),
        dependencies: this.getDependencies()
      },
      files: {
        included: [],
        excluded: [],
        totalSize: 0
      },
      features: [
        'User Profile System',
        'OpenRouter API Integration',
        'WhatsApp Transfer',
        'Chat Export',
        'Conversation Memory',
        'Multi-Consultant Support',
        'Adição Contabilidade Branding'
      ]
    };

    return manifest;
  }

  getProjectVersion() {
    try {
      const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
      return packageJson.version || '1.0.0';
    } catch (error) {
      return '1.0.0';
    }
  }

  getDependencies() {
    try {
      const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
      return {
        dependencies: packageJson.dependencies || {},
        devDependencies: packageJson.devDependencies || {}
      };
    } catch (error) {
      return { dependencies: {}, devDependencies: {} };
    }
  }

  async createZipBackup() {
    return new Promise((resolve, reject) => {
      const zipPath = path.join(this.backupDir, `${this.backupName}.zip`);
      const output = fs.createWriteStream(zipPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => {
        console.log(`✅ Backup criado: ${zipPath}`);
        console.log(`📦 Tamanho: ${(archive.pointer() / 1024 / 1024).toFixed(2)} MB`);
        resolve(zipPath);
      });

      archive.on('error', (err) => {
        reject(err);
      });

      archive.pipe(output);

      // Adicionar arquivos ao zip
      const { includePatterns, excludePatterns } = this.getFilesToBackup();
      
      // Adicionar arquivos específicos
      const filesToAdd = [
        'package.json',
        'package-lock.json',
        'pnpm-lock.yaml',
        'next.config.mjs',
        'tailwind.config.ts',
        'tsconfig.json',
        '.env.example',
        'README.md'
      ];

      filesToAdd.forEach(file => {
        const filePath = path.join(this.projectRoot, file);
        if (fs.existsSync(filePath)) {
          archive.file(filePath, { name: file });
        }
      });

      // Adicionar diretórios
      const dirsToAdd = ['app', 'components', 'lib', 'public', 'scripts', 'styles'];
      
      dirsToAdd.forEach(dir => {
        const dirPath = path.join(this.projectRoot, dir);
        if (fs.existsSync(dirPath)) {
          archive.directory(dirPath, dir);
        }
      });

      // Adicionar manifest
      const manifest = this.generateManifest();
      archive.append(JSON.stringify(manifest, null, 2), { name: 'BACKUP_MANIFEST.json' });

      // Adicionar informações do backup
      const backupInfo = this.generateBackupInfo();
      archive.append(backupInfo, { name: 'BACKUP_INFO.md' });

      archive.finalize();
    });
  }

  generateBackupInfo() {
    return `# 📦 Backup Automatizado - Adição Contabilidade

## 📋 Informações do Backup

- **Versão**: ${this.version}
- **Data**: ${new Date().toLocaleDateString('pt-BR')}
- **Hora**: ${new Date().toLocaleTimeString('pt-BR')}
- **Nome**: ${this.backupName}

## 🚀 Sistema de Chat - Funcionalidades

### ✅ **Funcionalidades Principais**
- 👥 **Sistema Multi-Consultores** (Jurídico, Contábil, Auditoria)
- 👤 **Perfil Completo do Usuário** com personalização
- 🔗 **Integração OpenRouter API** (modelos gratuitos e pagos)
- 📱 **Transferência para WhatsApp** com histórico
- 📥 **Exportação de Conversas** em formato TXT
- 🧠 **Memória de Contexto** para conversas inteligentes
- 🏢 **Branding Adição Contabilidade** integrado

### ✅ **Consultores Disponíveis**
- 👨‍⚖️ **Dr. Carlos Silva** - Consultor Jurídico
- 👩‍💼 **Dra. Ana Santos** - Contadora
- 👨‍💼 **Dr. Roberto Lima** - Auditor Fiscal

### ✅ **Tecnologias**
- **Framework**: Next.js 15.2.4
- **UI**: Radix UI + Tailwind CSS
- **Language**: TypeScript
- **API**: OpenRouter
- **Package Manager**: pnpm

## 🔧 Restauração

1. Extrair o arquivo ZIP
2. Executar \`pnpm install\`
3. Configurar \`.env.local\` com chave OpenRouter
4. Executar \`pnpm dev\`

## 📝 Notas

- Backup criado automaticamente pelo sistema
- Inclui todos os arquivos de código fonte
- Manifesto detalhado incluído
- Pronto para restauração completa

---

**Backup gerado em ${new Date().toLocaleString('pt-BR')}**
`;
  }

  async run() {
    try {
      console.log('🚀 Iniciando backup automatizado...');
      console.log(`📁 Versão: ${this.version}`);
      console.log(`📅 Data: ${this.timestamp}`);
      
      this.createBackupDirectory();
      console.log('📂 Diretório de backup criado');
      
      const zipPath = await this.createZipBackup();
      console.log('✅ Backup ZIP criado com sucesso!');
      
      // Remover diretório temporário se existir
      if (fs.existsSync(this.backupPath)) {
        fs.rmSync(this.backupPath, { recursive: true });
      }
      
      console.log(`\n🎉 Backup completo!`);
      console.log(`📦 Arquivo: ${zipPath}`);
      console.log(`🔢 Versão: ${this.version}`);
      
      return zipPath;
    } catch (error) {
      console.error('❌ Erro ao criar backup:', error);
      throw error;
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const backup = new BackupSystem();
  backup.run().catch(console.error);
}

module.exports = BackupSystem;
