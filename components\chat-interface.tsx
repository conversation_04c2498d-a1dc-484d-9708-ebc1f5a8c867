"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Send, Loader2, MessageCircle, Download } from "lucide-react"
import { toast } from "sonner"
import { FileUpload, FilePreview } from "@/components/file-upload"
import type { Consultor } from "@/app/page"

interface UploadedFile {
  name: string
  size: number
  type: string
  category: 'image' | 'document'
  url: string
  content?: string
  uploadedAt: string
}

interface Message {
  id: string
  content: string
  sender: "user" | "consultant"
  timestamp: Date
  file?: UploadedFile
}

interface ChatInterfaceProps {
  consultor: Consultor
  onBack: () => void
}

export function ChatInterface({ consultor, onBack }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: `Olá! Sou ${consultor.name}, ${consultor.specialty.toLowerCase()}. Como posso ajudá-lo hoje?`,
      sender: "consultant",
      timestamp: new Date(),
    },
  ])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [userProfile, setUserProfile] = useState<any>(null)
  const [pendingFile, setPendingFile] = useState<UploadedFile | null>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    // Carregar perfil do usuário
    const savedProfile = localStorage.getItem("userProfile")
    if (savedProfile) {
      try {
        setUserProfile(JSON.parse(savedProfile))
      } catch (error) {
        // Silently handle profile loading errors
      }
    }
  }, [])

  const handleFileUploaded = (file: UploadedFile) => {
    setPendingFile(file)
    toast.success(`Arquivo "${file.name}" carregado. Adicione uma mensagem e envie.`)
  }

  const removePendingFile = () => {
    setPendingFile(null)
  }

  const sendMessage = async () => {
    if ((!inputValue.trim() && !pendingFile) || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue || (pendingFile ? `[Arquivo enviado: ${pendingFile.name}]` : ""),
      sender: "user",
      timestamp: new Date(),
      file: pendingFile || undefined,
    }

    setMessages((prev) => [...prev, userMessage])
    setInputValue("")
    setPendingFile(null)
    setIsLoading(true)

    try {
      // Preparar histórico completo da conversa para contexto
      const allMessages = [...messages, userMessage]

      // Limitar a 20 mensagens mais recentes para evitar excesso de tokens
      const recentMessages = allMessages.slice(-20)

      // Formatar histórico para envio à API
      const conversationHistory = recentMessages
        .map((msg) => {
          let content = msg.content

          // Adicionar informações do arquivo se presente
          if (msg.file) {
            if (msg.file.category === 'document' && msg.file.content) {
              content += `\n\n[Documento anexado: ${msg.file.name}]\nConteúdo: ${msg.file.content}`
            } else if (msg.file.category === 'image') {
              content += `\n\n[Imagem anexada: ${msg.file.name}] - Analise esta imagem para o contexto da conversa.`
            }
          }

          return {
            role: msg.sender === "user" ? "user" : "assistant",
            content: content,
            timestamp: msg.timestamp.toISOString(),
            sender: msg.sender === "user" ? "Usuário" : consultor.name
          }
        })

      // Criar string de contexto para o prompt
      const contextString = recentMessages
        .map((msg) => {
          let msgText = `${msg.sender === "user" ? "Usuário" : consultor.name}: ${msg.content}`
          if (msg.file) {
            msgText += ` [Arquivo: ${msg.file.name}]`
          }
          return msgText
        })
        .join("\n")

      // Recuperar configurações salvas
      const selectedModel = localStorage.getItem("selectedModel") || "deepseek/deepseek-chat-v3-0324:free"
      const openrouterApiKey = localStorage.getItem("openrouterApiKey") || ""
      const customUrl = localStorage.getItem("customUrl") || ""
      const userProfileData = localStorage.getItem("userProfile")
      let userProfile = null

      if (userProfileData) {
        try {
          userProfile = JSON.parse(userProfileData)
        } catch (error) {
          // Silently handle profile loading errors
        }
      }

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: inputValue,
          systemPrompt: consultor.systemPrompt,
          conversationHistory: contextString,
          fullConversationHistory: conversationHistory,
          consultorName: consultor.name,
          model: selectedModel,
          customUrl: selectedModel === "custom" ? customUrl : undefined,
          apiKey: openrouterApiKey,
          userProfile: userProfile,
          imageUrl: userMessage.file?.category === 'image' ? `${window.location.origin}${userMessage.file.url}` : undefined,
        }),
      })

      let data
      try {
        data = await response.json()
      } catch (parseError) {
        console.error("Erro ao fazer parse da resposta:", parseError)
        throw new Error("Erro ao processar resposta do servidor")
      }

      if (!response.ok) {
        const errorMessage = data?.error || "Erro na resposta da API"
        throw new Error(errorMessage)
      }

      if (!data?.response) {
        throw new Error("Resposta inválida do servidor")
      }

      const consultantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.response,
        sender: "consultant",
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, consultantMessage])
    } catch (error) {
      let errorContent = "Desculpe, ocorreu um erro. Tente novamente."

      if (error instanceof Error) {
        // Mostrar mensagens de erro específicas para o usuário
        if (error.message.includes("API")) {
          errorContent = "Erro na comunicação com o servidor. Verifique sua conexão e tente novamente."
        } else if (error.message.includes("chave")) {
          errorContent = "Problema com a chave da API. Verifique suas configurações."
        } else if (error.message.includes("créditos")) {
          errorContent = "Créditos insuficientes na sua conta OpenRouter."
        } else if (error.message.includes("limite")) {
          errorContent = "Limite de requisições excedido. Tente novamente em alguns minutos."
        } else {
          errorContent = error.message
        }
      }

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: errorContent,
        sender: "consultant",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const transferToWhatsApp = () => {
    if (!userProfile?.supportPhone) {
      alert("Número de telefone não configurado. Configure nas configurações.")
      return
    }

    // Formatar conversa para envio
    const conversationText = messages
      .map(msg => {
        const time = msg.timestamp.toLocaleTimeString('pt-BR', {
          hour: '2-digit',
          minute: '2-digit'
        })
        const sender = msg.sender === 'user' ?
          (userProfile?.userName || 'Cliente') :
          consultor.name

        let msgText = `[${time}] ${sender}: ${msg.content}`

        // Adicionar informação de arquivo se presente
        if (msg.file) {
          msgText += ` 📎 [Arquivo: ${msg.file.name}]`
        }

        return msgText
      })
      .join('\n')

    const whatsappMessage = `*Transferência de Conversa - Adição Contabilidade*\n\n` +
      `Cliente: ${userProfile?.userName || 'Não informado'}\n` +
      `Empresa: ${userProfile?.companyName || 'Não informada'}\n` +
      `Consultor: ${consultor.name}\n\n` +
      `*Histórico da Conversa:*\n${conversationText}\n\n` +
      `_Conversa transferida automaticamente do sistema de chat._`

    // Limpar número de telefone (remover caracteres especiais)
    const cleanPhone = userProfile.supportPhone.replace(/\D/g, '')

    // Criar URL do WhatsApp
    const whatsappUrl = `https://wa.me/55${cleanPhone}?text=${encodeURIComponent(whatsappMessage)}`

    // Abrir WhatsApp
    window.open(whatsappUrl, '_blank')
  }

  const exportConversation = () => {
    if (messages.length <= 1) {
      alert("Não há conversa suficiente para exportar.")
      return
    }

    // Criar conteúdo do arquivo
    const now = new Date()
    const dateStr = now.toLocaleDateString('pt-BR')
    const timeStr = now.toLocaleTimeString('pt-BR')

    let content = `CONVERSA EXPORTADA - ADIÇÃO CONTABILIDADE\n`
    content += `${'='.repeat(50)}\n\n`
    content += `Data da Exportação: ${dateStr} às ${timeStr}\n`
    content += `Consultor: ${consultor.name}\n`
    content += `Especialidade: ${consultor.specialty}\n`

    if (userProfile?.userName) {
      content += `Cliente: ${userProfile.userName}\n`
    }
    if (userProfile?.companyName) {
      content += `Empresa: ${userProfile.companyName}\n`
    }

    content += `\n${'='.repeat(50)}\n`
    content += `HISTÓRICO DA CONVERSA\n`
    content += `${'='.repeat(50)}\n\n`

    // Adicionar mensagens
    messages.forEach((msg, index) => {
      const time = msg.timestamp.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      const date = msg.timestamp.toLocaleDateString('pt-BR')
      const sender = msg.sender === 'user' ?
        (userProfile?.userName || 'Cliente') :
        consultor.name

      content += `[${date} ${time}] ${sender}:\n`

      // Adicionar informações do arquivo se presente
      if (msg.file) {
        content += `📎 Arquivo anexado: ${msg.file.name}\n`
        content += `   Tipo: ${msg.file.category === 'image' ? 'Imagem' : 'Documento'}\n`
        content += `   Tamanho: ${(msg.file.size / 1024).toFixed(1)} KB\n`
        if (msg.file.content && msg.file.category === 'document') {
          content += `   Conteúdo: ${msg.file.content.substring(0, 200)}${msg.file.content.length > 200 ? '...' : ''}\n`
        }
        content += `\n`
      }

      content += `${msg.content}\n\n`

      if (index < messages.length - 1) {
        content += `${'-'.repeat(30)}\n\n`
      }
    })

    content += `\n${'='.repeat(50)}\n`
    content += `Fim da Conversa\n`
    content += `Exportado automaticamente pelo sistema da Adição Contabilidade\n`

    // Criar e baixar arquivo
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')

    // Nome do arquivo
    const consultorNameClean = consultor.name.replace(/[^a-zA-Z0-9]/g, '_')
    const dateForFile = now.toISOString().split('T')[0]
    const timeForFile = now.toTimeString().split(' ')[0].replace(/:/g, '-')
    const filename = `Conversa_${consultorNameClean}_${dateForFile}_${timeForFile}.txt`

    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <div className="flex flex-col h-screen">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4 shadow-sm">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="text-primary-foreground hover:bg-primary-foreground/10 p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>

          <Avatar className="h-10 w-10">
            <AvatarImage src={consultor.avatar || "/placeholder.svg"} alt={consultor.name} />
            <AvatarFallback className="bg-primary-foreground text-primary">
              {consultor.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <h2 className="font-semibold truncate">{consultor.name}</h2>
            <p className="text-sm opacity-90 truncate">{consultor.specialty}</p>
          </div>

          <div className="flex items-center gap-2">
            {/* Export Conversation Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={exportConversation}
              className="text-primary-foreground hover:bg-primary-foreground/10 p-2"
              title="Exportar conversa"
              disabled={messages.length <= 1}
            >
              <Download className="h-5 w-5" />
            </Button>

            {/* Transfer to WhatsApp Button */}
            {userProfile?.supportPhone && (
              <Button
                variant="ghost"
                size="sm"
                onClick={transferToWhatsApp}
                className="text-primary-foreground hover:bg-primary-foreground/10 p-2"
                title="Transferir para atendimento humano"
              >
                <MessageCircle className="h-5 w-5" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto bg-muted/20 p-4 space-y-4">
        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}>
            <div
              className={`max-w-[80%] rounded-lg p-3 shadow-sm ${
                message.sender === "user"
                  ? "bg-accent text-accent-foreground"
                  : "bg-card text-card-foreground border border-border"
              }`}
            >
              {/* Arquivo anexado */}
              {message.file && (
                <div className="mb-3">
                  <FilePreview file={message.file} />
                </div>
              )}

              {/* Conteúdo da mensagem */}
              {message.content && (
                <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
              )}

              <p
                className={`text-xs mt-1 ${
                  message.sender === "user" ? "text-accent-foreground/70" : "text-muted-foreground"
                }`}
              >
                {formatTime(message.timestamp)}
              </p>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-card text-card-foreground border border-border rounded-lg p-3 shadow-sm">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm text-muted-foreground">Digitando...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 bg-card border-t border-border">
        {/* Arquivo pendente */}
        {pendingFile && (
          <div className="mb-3">
            <FilePreview
              file={pendingFile}
              onRemove={removePendingFile}
              showRemove={true}
            />
          </div>
        )}

        <div className="flex gap-2">
          <FileUpload
            onFileUploaded={handleFileUploaded}
            disabled={isLoading}
          />
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={pendingFile ? "Adicione uma mensagem (opcional)..." : "Digite sua mensagem..."}
            disabled={isLoading}
            className="flex-1"
          />
          <Button
            onClick={sendMessage}
            disabled={(!inputValue.trim() && !pendingFile) || isLoading}
            size="sm"
            className="px-3"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
