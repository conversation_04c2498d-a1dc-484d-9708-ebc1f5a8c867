# Dockerfile para Produção - Adição Contabilidade
# ===============================================

# Usar imagem oficial do Node.js Alpine (mais leve)
FROM node:18-alpine AS base

# Instalar dependências necessárias
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Instalar pnpm
RUN npm install -g pnpm

# Copiar arquivos de dependências
COPY package.json pnpm-lock.yaml* ./

# Stage 1: Instalar dependências
FROM base AS deps
RUN pnpm install --frozen-lockfile

# Stage 2: Build da aplicação
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Configurar variáveis de ambiente para build
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV __NEXT_DEV_INDICATOR=false

# Build da aplicação
RUN pnpm build

# Stage 3: Imagem de produção
FROM base AS runner
WORKDIR /app

# Configurar usuário não-root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copiar arquivos necessários
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Criar diretório de uploads
RUN mkdir -p ./public/uploads
RUN chown nextjs:nodejs ./public/uploads

# Configurar permissões
USER nextjs

# Expor porta
EXPOSE 3000

# Configurar variáveis de ambiente
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV __NEXT_DEV_INDICATOR=false
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Comando para iniciar a aplicação
CMD ["node", "server.js"]
