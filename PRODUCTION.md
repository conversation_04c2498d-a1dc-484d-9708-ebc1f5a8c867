# 🚀 Guia de Deploy para Produção - Adição Contabilidade

## 📋 Pré-requisitos

### Ambiente
- **Node.js**: 18+ (recomendado: 20 LTS)
- **pnpm**: 8+ (gerenciador de pacotes)
- **Docker**: 20+ (opcional, para containerização)

### Configurações Necessárias
- Chave da API OpenRouter para produção
- Domínio configurado (HTTPS obrigatório)
- Servidor com pelo menos 2GB RAM

## 🔧 Configuração de Produção

### 1. Variáveis de Ambiente

Copie e configure o arquivo `.env.production`:

```bash
cp .env.production .env.local
```

**Variáveis obrigatórias:**
```env
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://seu-dominio.com
OPENROUTER_API_KEY=sk-or-v1-sua-chave-de-producao
NEXTAUTH_SECRET=sua-chave-secreta-minimo-32-caracteres
```

### 2. Build de Produção

Execute o script automatizado:

```bash
# Build completo com verificações
pnpm build:production

# Ou build manual
pnpm build:prod
```

### 3. Teste Local

Teste a aplicação em modo produção:

```bash
pnpm start:prod
```

Acesse: `http://localhost:3000`

## 🐳 Deploy com Docker

### Build da Imagem

```bash
# Build da imagem
docker build -t adicao-contabilidade .

# Executar container
docker run -p 3000:3000 \
  -e NODE_ENV=production \
  -e NEXT_PUBLIC_SITE_URL=https://seu-dominio.com \
  -e OPENROUTER_API_KEY=sua-chave \
  adicao-contabilidade
```

### Docker Compose

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SITE_URL=https://seu-dominio.com
      - OPENROUTER_API_KEY=sua-chave
    volumes:
      - ./uploads:/app/public/uploads
    restart: unless-stopped
```

## ☁️ Deploy em Plataformas

### Vercel (Recomendado)

1. **Conectar repositório** no Vercel
2. **Configurar variáveis de ambiente**:
   - `OPENROUTER_API_KEY`
   - `NEXTAUTH_SECRET`
3. **Deploy automático** a cada push

### Netlify

1. **Build command**: `pnpm build`
2. **Publish directory**: `.next`
3. **Configurar variáveis** no painel

### VPS/Servidor Próprio

```bash
# Clonar repositório
git clone seu-repositorio.git
cd consultant-chat-app

# Instalar dependências
pnpm install --frozen-lockfile

# Build para produção
pnpm build:production

# Iniciar com PM2
pm2 start ecosystem.config.js
```

## 🔒 Configurações de Segurança

### Headers de Segurança
Já configurados no `next.config.mjs`:
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Referrer-Policy: origin-when-cross-origin

### HTTPS
**Obrigatório em produção**. Configure:
- Certificado SSL/TLS
- Redirecionamento HTTP → HTTPS
- HSTS headers

### Firewall
Configure regras para:
- Porta 3000 (aplicação)
- Porta 443 (HTTPS)
- Bloquear portas desnecessárias

## 📊 Monitoramento

### Logs
```bash
# Visualizar logs em produção
pm2 logs adicao-contabilidade

# Logs do Docker
docker logs container-name
```

### Métricas
- **CPU/RAM**: Monitorar uso de recursos
- **Uptime**: Disponibilidade da aplicação
- **Response Time**: Performance das APIs

### Alertas
Configure alertas para:
- Aplicação offline
- Alto uso de CPU/RAM
- Erros 5xx frequentes

## 🔄 Atualizações

### Deploy Automático
```bash
# Script de atualização
git pull origin main
pnpm install --frozen-lockfile
pnpm build:production
pm2 restart adicao-contabilidade
```

### Rollback
```bash
# Voltar para versão anterior
git checkout versao-anterior
pnpm build:production
pm2 restart adicao-contabilidade
```

## 🛠️ Manutenção

### Backup
```bash
# Backup automático
pnpm backup

# Backup manual
tar -czf backup-$(date +%Y%m%d).tar.gz .
```

### Limpeza
```bash
# Limpar cache
pnpm clean

# Limpar uploads antigos (opcional)
find public/uploads -type f -mtime +30 -delete
```

## 📈 Otimizações

### Performance
- ✅ **Compressão** habilitada
- ✅ **Cache** configurado
- ✅ **Imagens** otimizadas
- ✅ **Bundle** minificado

### SEO
- ✅ **Meta tags** configuradas
- ✅ **Sitemap** gerado
- ✅ **Robots.txt** configurado

## 🆘 Troubleshooting

### Problemas Comuns

**Erro de build:**
```bash
# Limpar cache e rebuildar
pnpm clean
pnpm install
pnpm build:production
```

**Aplicação não inicia:**
- Verificar variáveis de ambiente
- Verificar porta disponível
- Verificar logs de erro

**Upload de arquivos falha:**
- Verificar permissões do diretório
- Verificar espaço em disco
- Verificar limites de upload

### Contato
Para suporte técnico, consulte a documentação ou entre em contato com a equipe de desenvolvimento.

---

**✅ Aplicação configurada para produção com:**
- Logs otimizados
- Performance maximizada
- Segurança robusta
- Monitoramento completo
