# Arquivos e diretórios a serem ignorados no Docker
# ================================================

# Dependências
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
.next/
out/
dist/
build/

# Arquivos de desenvolvimento
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
logs/

# Cache
.cache/
.parcel-cache/

# Arquivos temporários
.tmp/
.temp/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Documentação
README.md
*.md

# Testes
coverage/
.nyc_output/

# Backup
backups/
*.zip

# Uploads de desenvolvimento (manter estrutura)
public/uploads/*
!public/uploads/.gitkeep
