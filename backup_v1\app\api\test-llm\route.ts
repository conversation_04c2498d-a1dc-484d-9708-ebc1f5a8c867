import { type NextRequest, NextResponse } from "next/server"

function generateSystemPrompt(userProfile: any) {
  if (!userProfile) {
    return "Você é um assistente útil. Responda de forma concisa e clara."
  }

  const businessTypeMap: { [key: string]: string } = {
    commerce: "comércio/varejo",
    industry: "indústria/manufatura",
    services: "serviços",
    technology: "tecnologia",
    healthcare: "saúde",
    education: "educação",
    other: userProfile.customBusinessType || "outro"
  }

  const companySizeMap: { [key: string]: string } = {
    individual: "profissional autônomo/freelancer",
    small: "pequena empresa (1-10 funcionários)",
    medium: "média empresa (11-50 funcionários)",
    large: "grande empresa (50+ funcionários)"
  }

  const communicationStyleMap: { [key: string]: string } = {
    formal: "formal e profissional",
    casual: "casual e amigável",
    technical: "técnico e detalhado",
    simple: "simples e conciso"
  }

  const languageComplexityMap: { [key: string]: string } = {
    simple: "linguagem simples e acessível",
    professional: "terminologia profissional",
    technical: "jargão técnico quando apropriado"
  }

  const businessType = businessTypeMap[userProfile.businessType] || "negócio"
  const companySize = companySizeMap[userProfile.companySize] || "empresa"
  const communicationStyle = communicationStyleMap[userProfile.communicationStyle] || "profissional"
  const languageComplexity = languageComplexityMap[userProfile.languageComplexity] || "profissional"

  let prompt = `Você é um consultor da Adição Contabilidade especializado em ${businessType}. `

  if (userProfile.userName && userProfile.companyName) {
    prompt += `Você está atendendo ${userProfile.userName} da empresa ${userProfile.companyName}. `
  } else {
    prompt += `Você está atendendo um cliente de uma ${companySize}. `
  }

  prompt += `Mantenha um tom ${communicationStyle} e use ${languageComplexity}. `

  if (userProfile.industrySpecialization) {
    prompt += `O cliente atua especificamente em: ${userProfile.industrySpecialization}. `
  }

  prompt += "Sempre se identifique como consultor da Adição Contabilidade. "
  prompt += "Quando necessário, ofereça transferir a conversa para um especialista humano da empresa. "
  prompt += "Forneça respostas práticas, relevantes e adaptadas ao contexto do negócio do cliente."

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    const { message, model, customUrl, apiKey, userProfile } = await request.json()

    // Log apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log("[DEBUG] Teste LLM - Dados recebidos:", {
        message,
        model,
        customUrl,
        apiKey: apiKey ? "***" : "não fornecida",
        userProfile: userProfile ? "perfil fornecido" : "sem perfil"
      })
    }

    if (!message || !model) {
      return NextResponse.json({ error: "Mensagem e modelo são obrigatórios" }, { status: 400 })
    }

    const apiUrl = model === "custom" && customUrl ? customUrl : "https://openrouter.ai/api/v1/chat/completions"

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    if (model !== "custom") {
      // Usa a chave fornecida pelo usuário ou a variável de ambiente como fallback
      const openrouterKey = apiKey || process.env.OPENROUTER_API_KEY

      if (!openrouterKey) {
        return NextResponse.json({
          error: "Chave da API OpenRouter é obrigatória para todos os modelos. Por favor, insira sua chave nas configurações."
        }, { status: 400 })
      }

      // Validar formato da chave da API
      if (!openrouterKey.startsWith("sk-or-v1-")) {
        return NextResponse.json({
          error: "Formato da chave da API inválido. A chave deve começar com 'sk-or-v1-'"
        }, { status: 400 })
      }

      headers["Authorization"] = `Bearer ${openrouterKey}`
      headers["HTTP-Referer"] = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3001"
      headers["X-Title"] = "Consultoria Especializada"
    }

    if (process.env.NODE_ENV === 'development') {
      console.log("[DEBUG] Fazendo requisição para:", apiUrl)
      console.log("[DEBUG] Headers:", headers)
    }

    const systemPrompt = generateSystemPrompt(userProfile)

    const requestBody = {
      model: model === "custom" ? "gpt-3.5-turbo" : model,
      messages: [
        {
          role: "system",
          content: systemPrompt,
        },
        {
          role: "user",
          content: message,
        },
      ],
      max_tokens: 500,
      temperature: 0.7,
    }

    if (process.env.NODE_ENV === 'development') {
      console.log("[DEBUG] Body da requisição:", JSON.stringify(requestBody, null, 2))
    }

    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
    })

    if (process.env.NODE_ENV === 'development') {
      console.log("[DEBUG] Status da resposta:", response.status)
      console.log("[DEBUG] Headers da resposta:", Object.fromEntries(response.headers.entries()))
    }

    if (!response.ok) {
      const errorData = await response.text()
      if (process.env.NODE_ENV === 'development') {
        console.error("[DEBUG] Erro da API (texto):", errorData)
      }

      let errorMessage = "Erro desconhecido da API"

      try {
        const errorJson = JSON.parse(errorData)
        const apiError = errorJson.error?.message || errorJson.message || errorData

        // Tratar erros específicos da OpenRouter
        if (response.status === 401) {
          if (apiError.includes("User not found") || apiError.includes("Invalid API key")) {
            errorMessage = "Chave da API inválida. Verifique se sua chave OpenRouter está correta e ativa."
          } else {
            errorMessage = "Erro de autenticação. Verifique sua chave da API."
          }
        } else if (response.status === 402) {
          errorMessage = "Créditos insuficientes na sua conta OpenRouter."
        } else if (response.status === 429) {
          errorMessage = "Limite de requisições excedido. Tente novamente em alguns minutos."
        } else if (response.status === 400) {
          errorMessage = `Erro na requisição: ${apiError}`
        } else {
          errorMessage = `Erro da API (${response.status}): ${apiError}`
        }
      } catch (parseError) {
        errorMessage = `Erro da API (${response.status}): ${errorData}`
      }

      return NextResponse.json({ error: errorMessage }, { status: response.status })
    }

    const responseText = await response.text()

    if (process.env.NODE_ENV === 'development') {
      console.log("[DEBUG] Resposta bruta:", responseText)
    }

    let data
    try {
      data = JSON.parse(responseText)
      if (process.env.NODE_ENV === 'development') {
        console.log("[DEBUG] JSON parseado com sucesso:", data)
      }
    } catch (parseError) {
      if (process.env.NODE_ENV === 'development') {
        console.error("[DEBUG] Erro ao fazer parse do JSON:", parseError)
        console.error("[DEBUG] Conteúdo que causou erro:", responseText.substring(0, 500))
      }
      return NextResponse.json({ error: "Resposta inválida da API" }, { status: 500 })
    }

    const assistantMessage = data.choices?.[0]?.message?.content || "Sem resposta"

    if (process.env.NODE_ENV === 'development') {
      console.log("[DEBUG] Mensagem extraída:", assistantMessage)
    }

    return NextResponse.json({ response: assistantMessage })
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error("[DEBUG] Erro no teste da LLM:", error)
    }
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
