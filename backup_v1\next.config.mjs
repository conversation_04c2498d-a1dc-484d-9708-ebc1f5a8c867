/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Configurações para produção
  productionBrowserSourceMaps: false,
  poweredByHeader: false,
  compress: true,
  // Otimizações de bundle
  experimental: {
    optimizeCss: true,
  },
}

export default nextConfig
