#!/usr/bin/env node

/**
 * Script de Build para Produção - Adição Contabilidade
 * ===================================================
 * 
 * Este script automatiza o processo de build para produção,
 * incluindo limpeza, otimizações e verificações.
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Cores para output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function step(message) {
  log(`\n🔄 ${message}`, 'cyan')
}

function success(message) {
  log(`✅ ${message}`, 'green')
}

function error(message) {
  log(`❌ ${message}`, 'red')
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function runCommand(command, description) {
  try {
    step(description)
    execSync(command, { stdio: 'inherit' })
    success(`${description} - Concluído`)
  } catch (err) {
    error(`${description} - Falhou`)
    process.exit(1)
  }
}

function checkEnvironment() {
  step('Verificando ambiente de produção')
  
  // Verificar se existe .env.production
  if (!fs.existsSync('.env.production')) {
    warning('Arquivo .env.production não encontrado')
    warning('Certifique-se de configurar as variáveis de ambiente para produção')
  } else {
    success('Arquivo .env.production encontrado')
  }
  
  // Verificar Node.js version
  const nodeVersion = process.version
  log(`Node.js version: ${nodeVersion}`, 'blue')
  
  // Verificar se é uma versão suportada
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
  if (majorVersion < 18) {
    error('Node.js 18+ é requerido para produção')
    process.exit(1)
  }
  
  success('Ambiente verificado')
}

function cleanBuild() {
  step('Limpando builds anteriores')
  
  const dirsToClean = ['.next', 'out', 'dist']
  
  dirsToClean.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true })
      log(`Removido: ${dir}`, 'yellow')
    }
  })
  
  success('Limpeza concluída')
}

function installDependencies() {
  runCommand('pnpm install --frozen-lockfile', 'Instalando dependências')
}

function typeCheck() {
  runCommand('pnpm type-check', 'Verificação de tipos TypeScript')
}

function buildApplication() {
  // Configurar variáveis de ambiente para build
  process.env.NODE_ENV = 'production'
  process.env.NEXT_TELEMETRY_DISABLED = '1'
  process.env.__NEXT_DEV_INDICATOR = 'false'
  
  runCommand('pnpm build:prod', 'Build da aplicação para produção')
}

function optimizeAssets() {
  step('Otimizando assets')
  
  // Verificar se o diretório .next existe
  if (!fs.existsSync('.next')) {
    error('Diretório .next não encontrado. Build falhou?')
    process.exit(1)
  }
  
  // Estatísticas do build
  const nextDir = '.next'
  const stats = fs.statSync(nextDir)
  log(`Build size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`, 'blue')
  
  success('Assets otimizados')
}

function createProductionInfo() {
  step('Criando informações de produção')
  
  const buildInfo = {
    buildDate: new Date().toISOString(),
    nodeVersion: process.version,
    environment: 'production',
    version: require('../package.json').version,
    name: require('../package.json').name,
  }
  
  fs.writeFileSync(
    'public/build-info.json',
    JSON.stringify(buildInfo, null, 2)
  )
  
  success('Informações de produção criadas')
}

function main() {
  log('\n🚀 Build de Produção - Adição Contabilidade', 'bright')
  log('=' .repeat(50), 'blue')
  
  try {
    checkEnvironment()
    cleanBuild()
    installDependencies()
    typeCheck()
    buildApplication()
    optimizeAssets()
    createProductionInfo()
    
    log('\n' + '='.repeat(50), 'green')
    success('🎉 Build de produção concluído com sucesso!')
    log('\nPróximos passos:', 'bright')
    log('1. Teste a aplicação: pnpm start:prod', 'blue')
    log('2. Configure as variáveis de ambiente de produção', 'blue')
    log('3. Deploy para seu servidor/plataforma', 'blue')
    
  } catch (err) {
    error('Build de produção falhou')
    console.error(err)
    process.exit(1)
  }
}

// Executar script
if (require.main === module) {
  main()
}

module.exports = { main }
