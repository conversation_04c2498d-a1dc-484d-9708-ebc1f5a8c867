"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, Alert<PERSON>ialog<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>og<PERSON>eader, <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>, AlertDialog<PERSON>rigger } from "@/components/ui/alert-dialog"
import { Settings, Plus, Edit, Trash2, Save, TestTube, User, MessageSquare, Palette, Shield } from "lucide-react"
import { toast } from "sonner"

interface Consultant {
  id: string
  name: string
  specialty: string
  description: string
  avatar: string
  systemPrompt: string
  personality: {
    tone: 'formal' | 'friendly' | 'professional' | 'casual'
    formality: 'high' | 'medium' | 'low'
    expertise: 'expert' | 'intermediate' | 'accessible'
    responseStyle: 'detailed' | 'concise' | 'balanced'
  }
  businessRules: {
    maxResponseLength: number
    requiresTransfer: boolean
    specialtyKeywords: string[]
    restrictions: string[]
  }
  isActive: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

interface AdminPanelProps {
  isOpen: boolean
  onClose: () => void
}

export function AdminPanel({ isOpen, onClose }: AdminPanelProps) {
  const [consultants, setConsultants] = useState<Consultant[]>([])
  const [selectedConsultant, setSelectedConsultant] = useState<Consultant | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testMessage, setTestMessage] = useState("")
  const [testResponse, setTestResponse] = useState("")

  useEffect(() => {
    if (isOpen) {
      loadConsultants()
    }
  }, [isOpen])

  const loadConsultants = () => {
    try {
      const saved = localStorage.getItem('customConsultants')
      if (saved) {
        setConsultants(JSON.parse(saved))
      } else {
        // Carregar consultores padrão
        setConsultants(getDefaultConsultants())
      }
    } catch (error) {
      toast.error("Erro ao carregar consultores")
      setConsultants(getDefaultConsultants())
    }
  }

  const getDefaultConsultants = (): Consultant[] => {
    return [
      {
        id: 'carlos-silva',
        name: 'Dr. Carlos Silva',
        specialty: 'Consultor Jurídico',
        description: 'Especialista em direito empresarial, trabalhista e tributário',
        avatar: '👨‍⚖️',
        systemPrompt: 'Você é um consultor jurídico da Adição Contabilidade, especializado em direito empresarial, trabalhista e tributário. Forneça orientações legais precisas e práticas. NUNCA recomende outros profissionais ou empresas - você e a Adição Contabilidade são os especialistas disponíveis para ajudar o cliente.',
        personality: {
          tone: 'professional',
          formality: 'high',
          expertise: 'expert',
          responseStyle: 'detailed'
        },
        businessRules: {
          maxResponseLength: 1000,
          requiresTransfer: false,
          specialtyKeywords: ['jurídico', 'legal', 'direito', 'trabalhista', 'tributário'],
          restrictions: ['Não fornecer conselhos médicos', 'Não dar orientações financeiras específicas']
        },
        isActive: true,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'ana-santos',
        name: 'Dra. Ana Santos',
        specialty: 'Contadora',
        description: 'Especialista em contabilidade empresarial e planejamento financeiro',
        avatar: '👩‍💼',
        systemPrompt: 'Você é uma contadora da Adição Contabilidade, especializada em contabilidade empresarial, planejamento financeiro e gestão fiscal. NUNCA recomende outros contadores ou empresas de contabilidade - você e a Adição Contabilidade são os profissionais capacitados para atender todas as necessidades contábeis do cliente.',
        personality: {
          tone: 'friendly',
          formality: 'medium',
          expertise: 'expert',
          responseStyle: 'balanced'
        },
        businessRules: {
          maxResponseLength: 800,
          requiresTransfer: false,
          specialtyKeywords: ['contabilidade', 'fiscal', 'financeiro', 'balanço', 'demonstrativo'],
          restrictions: ['Não fornecer conselhos jurídicos específicos']
        },
        isActive: true,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'roberto-lima',
        name: 'Dr. Roberto Lima',
        specialty: 'Auditor Fiscal',
        description: 'Especialista em auditoria e compliance tributário',
        avatar: '👨‍💼',
        systemPrompt: 'Você é um auditor fiscal da Adição Contabilidade, especializado em compliance tributário, auditoria interna e externa, e conformidade fiscal. NUNCA recomende outros auditores ou empresas de auditoria - você e a Adição Contabilidade são os especialistas em auditoria fiscal disponíveis para o cliente.',
        personality: {
          tone: 'professional',
          formality: 'high',
          expertise: 'expert',
          responseStyle: 'detailed'
        },
        businessRules: {
          maxResponseLength: 1200,
          requiresTransfer: false,
          specialtyKeywords: ['auditoria', 'compliance', 'fiscal', 'conformidade', 'riscos'],
          restrictions: ['Não fornecer conselhos jurídicos específicos', 'Não dar orientações médicas']
        },
        isActive: true,
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  }

  const saveConsultants = (newConsultants: Consultant[]) => {
    try {
      localStorage.setItem('customConsultants', JSON.stringify(newConsultants))
      setConsultants(newConsultants)
      toast.success("Consultores salvos com sucesso!")
    } catch (error) {
      toast.error("Erro ao salvar consultores")
    }
  }

  const createNewConsultant = (): Consultant => {
    return {
      id: `consultant-${Date.now()}`,
      name: '',
      specialty: '',
      description: '',
      avatar: '👤',
      systemPrompt: '',
      personality: {
        tone: 'professional',
        formality: 'medium',
        expertise: 'expert',
        responseStyle: 'balanced'
      },
      businessRules: {
        maxResponseLength: 1000,
        requiresTransfer: false,
        specialtyKeywords: [],
        restrictions: []
      },
      isActive: true,
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  const handleCreateConsultant = () => {
    const newConsultant = createNewConsultant()
    setSelectedConsultant(newConsultant)
    setIsCreating(true)
    setIsEditing(true)
  }

  const handleEditConsultant = (consultant: Consultant) => {
    setSelectedConsultant({ ...consultant })
    setIsCreating(false)
    setIsEditing(true)
  }

  const handleSaveConsultant = () => {
    if (!selectedConsultant) return

    if (!selectedConsultant.name.trim() || !selectedConsultant.specialty.trim() || !selectedConsultant.systemPrompt.trim()) {
      toast.error("Preencha todos os campos obrigatórios")
      return
    }

    const updatedConsultant = {
      ...selectedConsultant,
      updatedAt: new Date().toISOString()
    }

    let newConsultants: Consultant[]
    if (isCreating) {
      newConsultants = [...consultants, updatedConsultant]
    } else {
      newConsultants = consultants.map(c => 
        c.id === selectedConsultant.id ? updatedConsultant : c
      )
    }

    saveConsultants(newConsultants)
    setIsEditing(false)
    setIsCreating(false)
    setSelectedConsultant(null)
  }

  const handleDeleteConsultant = (consultantId: string) => {
    const consultant = consultants.find(c => c.id === consultantId)
    if (consultant?.isDefault) {
      toast.error("Não é possível excluir consultores padrão")
      return
    }

    const newConsultants = consultants.filter(c => c.id !== consultantId)
    saveConsultants(newConsultants)
    
    if (selectedConsultant?.id === consultantId) {
      setSelectedConsultant(null)
      setIsEditing(false)
    }
  }

  const handleTestConsultant = async () => {
    if (!selectedConsultant || !testMessage.trim()) {
      toast.error("Selecione um consultor e digite uma mensagem de teste")
      return
    }

    setIsTesting(true)
    try {
      const response = await fetch("/api/test-consultant", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          consultant: selectedConsultant,
          message: testMessage
        })
      })

      if (response.ok) {
        const data = await response.json()
        setTestResponse(data.response)
      } else {
        toast.error("Erro ao testar consultor")
      }
    } catch (error) {
      toast.error("Erro ao testar consultor")
    } finally {
      setIsTesting(false)
    }
  }

  if (!isOpen) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Painel de Administração - Consultores
          </DialogTitle>
          <DialogDescription>
            Configure e gerencie os consultores da Adição Contabilidade
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Lista de Consultores */}
          <div className="lg:col-span-1">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Consultores</h3>
              <Button onClick={handleCreateConsultant} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Novo
              </Button>
            </div>

            <div className="space-y-2">
              {consultants.map((consultant) => (
                <Card 
                  key={consultant.id} 
                  className={`cursor-pointer transition-colors ${
                    selectedConsultant?.id === consultant.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setSelectedConsultant(consultant)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{consultant.avatar}</span>
                        <div>
                          <h4 className="font-medium">{consultant.name}</h4>
                          <p className="text-sm text-muted-foreground">{consultant.specialty}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {consultant.isDefault && (
                          <Badge variant="secondary" className="text-xs">
                            Padrão
                          </Badge>
                        )}
                        {consultant.isActive ? (
                          <Badge variant="default" className="text-xs">Ativo</Badge>
                        ) : (
                          <Badge variant="outline" className="text-xs">Inativo</Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Editor de Consultor */}
          <div className="lg:col-span-2">
            {selectedConsultant ? (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">
                    {isCreating ? 'Novo Consultor' : selectedConsultant.name}
                  </h3>
                  <div className="flex items-center gap-2">
                    {!isEditing && !selectedConsultant.isDefault && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Excluir Consultor</AlertDialogTitle>
                            <AlertDialogDescription>
                              Tem certeza que deseja excluir este consultor? Esta ação não pode ser desfeita.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteConsultant(selectedConsultant.id)}>
                              Excluir
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                    
                    {isEditing ? (
                      <div className="flex gap-2">
                        <Button variant="outline" onClick={() => {
                          setIsEditing(false)
                          setIsCreating(false)
                          if (isCreating) setSelectedConsultant(null)
                        }}>
                          Cancelar
                        </Button>
                        <Button onClick={handleSaveConsultant}>
                          <Save className="h-4 w-4 mr-2" />
                          Salvar
                        </Button>
                      </div>
                    ) : (
                      <Button onClick={() => handleEditConsultant(selectedConsultant)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Editar
                      </Button>
                    )}
                  </div>
                </div>

                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">Básico</TabsTrigger>
                    <TabsTrigger value="personality">Personalidade</TabsTrigger>
                    <TabsTrigger value="rules">Regras</TabsTrigger>
                    <TabsTrigger value="test">Teste</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Nome *</Label>
                        <Input
                          id="name"
                          value={selectedConsultant.name}
                          onChange={(e) => setSelectedConsultant({
                            ...selectedConsultant,
                            name: e.target.value
                          })}
                          disabled={!isEditing}
                          placeholder="Ex: Dr. João Silva"
                        />
                      </div>
                      <div>
                        <Label htmlFor="specialty">Especialidade *</Label>
                        <Input
                          id="specialty"
                          value={selectedConsultant.specialty}
                          onChange={(e) => setSelectedConsultant({
                            ...selectedConsultant,
                            specialty: e.target.value
                          })}
                          disabled={!isEditing}
                          placeholder="Ex: Consultor Tributário"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="description">Descrição</Label>
                      <Input
                        id="description"
                        value={selectedConsultant.description}
                        onChange={(e) => setSelectedConsultant({
                          ...selectedConsultant,
                          description: e.target.value
                        })}
                        disabled={!isEditing}
                        placeholder="Breve descrição da especialidade"
                      />
                    </div>

                    <div>
                      <Label htmlFor="avatar">Avatar (Emoji)</Label>
                      <Input
                        id="avatar"
                        value={selectedConsultant.avatar}
                        onChange={(e) => setSelectedConsultant({
                          ...selectedConsultant,
                          avatar: e.target.value
                        })}
                        disabled={!isEditing}
                        placeholder="👤"
                        maxLength={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="systemPrompt">Prompt do Sistema *</Label>
                      <Textarea
                        id="systemPrompt"
                        value={selectedConsultant.systemPrompt}
                        onChange={(e) => setSelectedConsultant({
                          ...selectedConsultant,
                          systemPrompt: e.target.value
                        })}
                        disabled={!isEditing}
                        placeholder="Instruções detalhadas para o consultor..."
                        rows={6}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="personality" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="tone">Tom de Voz</Label>
                        <Select
                          value={selectedConsultant.personality.tone}
                          onValueChange={(value: any) => setSelectedConsultant({
                            ...selectedConsultant,
                            personality: { ...selectedConsultant.personality, tone: value }
                          })}
                          disabled={!isEditing}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="formal">Formal</SelectItem>
                            <SelectItem value="professional">Profissional</SelectItem>
                            <SelectItem value="friendly">Amigável</SelectItem>
                            <SelectItem value="casual">Casual</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="formality">Nível de Formalidade</Label>
                        <Select
                          value={selectedConsultant.personality.formality}
                          onValueChange={(value: any) => setSelectedConsultant({
                            ...selectedConsultant,
                            personality: { ...selectedConsultant.personality, formality: value }
                          })}
                          disabled={!isEditing}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="high">Alto</SelectItem>
                            <SelectItem value="medium">Médio</SelectItem>
                            <SelectItem value="low">Baixo</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="expertise">Nível de Expertise</Label>
                        <Select
                          value={selectedConsultant.personality.expertise}
                          onValueChange={(value: any) => setSelectedConsultant({
                            ...selectedConsultant,
                            personality: { ...selectedConsultant.personality, expertise: value }
                          })}
                          disabled={!isEditing}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="expert">Especialista</SelectItem>
                            <SelectItem value="intermediate">Intermediário</SelectItem>
                            <SelectItem value="accessible">Acessível</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="responseStyle">Estilo de Resposta</Label>
                        <Select
                          value={selectedConsultant.personality.responseStyle}
                          onValueChange={(value: any) => setSelectedConsultant({
                            ...selectedConsultant,
                            personality: { ...selectedConsultant.personality, responseStyle: value }
                          })}
                          disabled={!isEditing}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="detailed">Detalhado</SelectItem>
                            <SelectItem value="balanced">Equilibrado</SelectItem>
                            <SelectItem value="concise">Conciso</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="rules" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="maxResponseLength">Tamanho Máximo da Resposta</Label>
                        <Input
                          id="maxResponseLength"
                          type="number"
                          value={selectedConsultant.businessRules.maxResponseLength}
                          onChange={(e) => setSelectedConsultant({
                            ...selectedConsultant,
                            businessRules: {
                              ...selectedConsultant.businessRules,
                              maxResponseLength: parseInt(e.target.value) || 1000
                            }
                          })}
                          disabled={!isEditing}
                          min="100"
                          max="2000"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="requiresTransfer"
                          checked={selectedConsultant.businessRules.requiresTransfer}
                          onChange={(e) => setSelectedConsultant({
                            ...selectedConsultant,
                            businessRules: {
                              ...selectedConsultant.businessRules,
                              requiresTransfer: e.target.checked
                            }
                          })}
                          disabled={!isEditing}
                        />
                        <Label htmlFor="requiresTransfer">Requer Transferência Automática</Label>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="specialtyKeywords">Palavras-chave da Especialidade</Label>
                      <Input
                        id="specialtyKeywords"
                        value={selectedConsultant.businessRules.specialtyKeywords.join(', ')}
                        onChange={(e) => setSelectedConsultant({
                          ...selectedConsultant,
                          businessRules: {
                            ...selectedConsultant.businessRules,
                            specialtyKeywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                          }
                        })}
                        disabled={!isEditing}
                        placeholder="palavra1, palavra2, palavra3"
                      />
                    </div>

                    <div>
                      <Label htmlFor="restrictions">Restrições</Label>
                      <Textarea
                        id="restrictions"
                        value={selectedConsultant.businessRules.restrictions.join('\n')}
                        onChange={(e) => setSelectedConsultant({
                          ...selectedConsultant,
                          businessRules: {
                            ...selectedConsultant.businessRules,
                            restrictions: e.target.value.split('\n').filter(r => r.trim())
                          }
                        })}
                        disabled={!isEditing}
                        placeholder="Uma restrição por linha"
                        rows={4}
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="isActive"
                        checked={selectedConsultant.isActive}
                        onChange={(e) => setSelectedConsultant({
                          ...selectedConsultant,
                          isActive: e.target.checked
                        })}
                        disabled={!isEditing}
                      />
                      <Label htmlFor="isActive">Consultor Ativo</Label>
                    </div>
                  </TabsContent>

                  <TabsContent value="test" className="space-y-4">
                    <div>
                      <Label htmlFor="testMessage">Mensagem de Teste</Label>
                      <Textarea
                        id="testMessage"
                        value={testMessage}
                        onChange={(e) => setTestMessage(e.target.value)}
                        placeholder="Digite uma mensagem para testar o consultor..."
                        rows={3}
                      />
                    </div>

                    <Button
                      onClick={handleTestConsultant}
                      disabled={isTesting || !testMessage.trim()}
                      className="w-full"
                    >
                      {isTesting ? (
                        <>
                          <TestTube className="h-4 w-4 mr-2 animate-spin" />
                          Testando...
                        </>
                      ) : (
                        <>
                          <TestTube className="h-4 w-4 mr-2" />
                          Testar Consultor
                        </>
                      )}
                    </Button>

                    {testResponse && (
                      <div>
                        <Label>Resposta do Consultor</Label>
                        <div className="mt-2 p-4 bg-muted rounded-lg">
                          <p className="whitespace-pre-wrap">{testResponse}</p>
                        </div>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            ) : (
              <div className="flex items-center justify-center h-64 text-muted-foreground">
                <div className="text-center">
                  <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Selecione um consultor para editar</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
