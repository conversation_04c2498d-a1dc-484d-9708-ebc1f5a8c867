import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { message, systemPrompt, conversationHistory, consultorName, model, customUrl } = await request.json()

    if (model !== "custom" && !process.env.OPENROUTER_API_KEY) {
      return NextResponse.json({ error: "Chave da API OpenRouter não configurada" }, { status: 500 })
    }

    const apiUrl = model === "custom" && customUrl ? customUrl : "https://openrouter.ai/api/v1/chat/completions"

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    if (model !== "custom") {
      headers["Authorization"] = `Bearer ${process.env.OPENROUTER_API_KEY}`
      headers["HTTP-Referer"] = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"
      headers["X-Title"] = "Consultoria Especializada"
    }

    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify({
        model: model === "custom" ? "gpt-3.5-turbo" : model || "deepseek/deepseek-chat-v3-0324:free", // Handle custom model naming
        messages: [
          {
            role: "system",
            content: `${systemPrompt}

Histórico da conversa recente:
${conversationHistory}

Instruções adicionais:
- Responda sempre em português brasileiro
- Seja profissional, mas acessível
- Mantenha respostas concisas e práticas
- Se necessário, peça mais detalhes para dar uma orientação mais precisa
- Sempre considere o contexto das mensagens anteriores`,
          },
          {
            role: "user",
            content: message,
          },
        ],
        temperature: 0.7,
        max_tokens: 1000,
      }),
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error("Erro da API:", errorData)
      return NextResponse.json({ error: "Erro ao processar solicitação" }, { status: response.status })
    }

    const data = await response.json()
    const aiResponse = data.choices[0]?.message?.content || "Desculpe, não consegui processar sua solicitação."

    return NextResponse.json({ response: aiResponse })
  } catch (error) {
    console.error("Erro no endpoint de chat:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
