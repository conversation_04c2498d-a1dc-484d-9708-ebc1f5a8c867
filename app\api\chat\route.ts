import { type NextRequest, NextResponse } from "next/server"

function enhanceSystemPrompt(originalPrompt: string, userProfile: any) {
  if (!userProfile) {
    return originalPrompt
  }

  const businessTypeMap: { [key: string]: string } = {
    commerce: "comércio/varejo",
    industry: "indústria/manufatura",
    services: "serviços",
    technology: "tecnologia",
    healthcare: "saúde",
    education: "educação",
    other: userProfile.customBusinessType || "outro"
  }

  const companySizeMap: { [key: string]: string } = {
    individual: "profissional autônomo/freelancer",
    small: "pequena empresa (1-10 funcionários)",
    medium: "média empresa (11-50 funcionários)",
    large: "grande empresa (50+ funcionários)"
  }

  const communicationStyleMap: { [key: string]: string } = {
    formal: "formal e profissional",
    casual: "casual e amigável",
    technical: "técnico e detalhado",
    simple: "simples e conciso"
  }

  const languageComplexityMap: { [key: string]: string } = {
    simple: "linguagem simples e acessível",
    professional: "terminologia profissional",
    technical: "jargão técnico quando apropriado"
  }

  const businessType = businessTypeMap[userProfile.businessType] || "negócio"
  const companySize = companySizeMap[userProfile.companySize] || "empresa"
  const communicationStyle = communicationStyleMap[userProfile.communicationStyle] || "profissional"
  const languageComplexity = languageComplexityMap[userProfile.languageComplexity] || "profissional"

  let enhancement = `\n\nCONTEXTO DO CLIENTE:\n`

  if (userProfile.userName && userProfile.companyName) {
    enhancement += `- Cliente: ${userProfile.userName}\n`
    enhancement += `- Empresa: ${userProfile.companyName}\n`
  }

  enhancement += `- Tipo de negócio: ${businessType}\n`
  enhancement += `- Tamanho da empresa: ${companySize}\n`
  enhancement += `- Estilo de comunicação preferido: ${communicationStyle}\n`
  enhancement += `- Complexidade da linguagem: ${languageComplexity}\n`

  if (userProfile.industrySpecialization) {
    enhancement += `- Especialização: ${userProfile.industrySpecialization}\n`
  }

  enhancement += `\nIMPORTANTE:\n`
  enhancement += `- Você é um consultor da Adição Contabilidade\n`
  enhancement += `- Sempre se identifique como tal\n`
  enhancement += `- Quando apropriado, ofereça transferir para um especialista humano\n`
  enhancement += `- Adapte suas respostas considerando este contexto, mantendo o tom ${communicationStyle} e usando ${languageComplexity}.`

  return originalPrompt + enhancement
}

export async function POST(request: NextRequest) {
  try {
    const { message, systemPrompt, conversationHistory, fullConversationHistory, consultorName, model, customUrl, apiKey, userProfile } = await request.json()

    const apiUrl = model === "custom" && customUrl ? customUrl : "https://openrouter.ai/api/v1/chat/completions"

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    if (model !== "custom") {
      // Usa a chave fornecida pelo usuário ou a variável de ambiente como fallback
      const openrouterKey = apiKey || process.env.OPENROUTER_API_KEY

      if (!openrouterKey) {
        return NextResponse.json({
          error: "Chave da API OpenRouter é obrigatória para todos os modelos. Por favor, insira sua chave nas configurações."
        }, { status: 400 })
      }

      // Validar formato da chave da API
      if (!openrouterKey.startsWith("sk-or-v1-")) {
        return NextResponse.json({
          error: "Formato da chave da API inválido. A chave deve começar com 'sk-or-v1-'"
        }, { status: 400 })
      }

      headers["Authorization"] = `Bearer ${openrouterKey}`
      headers["HTTP-Referer"] = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3001"
      headers["X-Title"] = "Consultoria Especializada"
    }

    const enhancedSystemPrompt = enhanceSystemPrompt(systemPrompt, userProfile)

    // Construir mensagens incluindo histórico completo
    const messages = [
      {
        role: "system",
        content: `${enhancedSystemPrompt}

Instruções adicionais:
- Responda sempre em português brasileiro
- Seja profissional, mas acessível
- Mantenha respostas concisas e práticas
- Se necessário, peça mais detalhes para dar uma orientação mais precisa
- Considere todo o contexto da conversa anterior para dar respostas coerentes
- Mantenha a continuidade da conversa e referencie informações anteriores quando relevante`,
      }
    ]

    // Adicionar histórico completo da conversa se disponível
    if (fullConversationHistory && Array.isArray(fullConversationHistory)) {
      // Adicionar mensagens do histórico (exceto a última que é a atual)
      const historyMessages = fullConversationHistory.slice(0, -1).map(msg => ({
        role: msg.role,
        content: msg.content
      }))
      messages.push(...historyMessages)
    }

    // Adicionar a mensagem atual do usuário
    messages.push({
      role: "user",
      content: message,
    })

    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify({
        model: model === "custom" ? "gpt-3.5-turbo" : model || "deepseek/deepseek-chat-v3-0324:free",
        messages: messages,
        temperature: 0.7,
        max_tokens: 1000,
      }),
    })

    if (!response.ok) {
      const errorData = await response.text()
      // Erro da API processado

      let errorMessage = "Erro ao processar solicitação"

      try {
        const errorJson = JSON.parse(errorData)
        const apiError = errorJson.error?.message || errorJson.message || errorData

        // Tratar erros específicos da OpenRouter
        if (response.status === 401) {
          if (apiError.includes("User not found") || apiError.includes("Invalid API key")) {
            errorMessage = "Chave da API inválida. Verifique se sua chave OpenRouter está correta e ativa."
          } else {
            errorMessage = "Erro de autenticação. Verifique sua chave da API."
          }
        } else if (response.status === 402) {
          errorMessage = "Créditos insuficientes na sua conta OpenRouter."
        } else if (response.status === 429) {
          errorMessage = "Limite de requisições excedido. Tente novamente em alguns minutos."
        } else if (response.status === 400) {
          errorMessage = `Erro na requisição: ${apiError}`
        } else {
          errorMessage = `Erro da API: ${apiError}`
        }
      } catch (parseError) {
        errorMessage = `Erro da API (${response.status}): ${errorData}`
      }

      return NextResponse.json({ error: errorMessage }, { status: response.status })
    }

    const data = await response.json()
    const aiResponse = data.choices[0]?.message?.content || "Desculpe, não consegui processar sua solicitação."

    return NextResponse.json({ response: aiResponse })
  } catch (error) {
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
