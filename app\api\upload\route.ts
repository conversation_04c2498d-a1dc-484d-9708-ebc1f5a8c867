import { NextRequest, NextResponse } from "next/server"
import { writeFile, mkdir } from "fs/promises"
import { existsSync } from "fs"
import path from "path"
import { logger } from "@/lib/logger"

// Configurações de upload
const MAX_FILE_SIZE = {
  image: 10 * 1024 * 1024, // 10MB para imagens
  document: 5 * 1024 * 1024, // 5MB para documentos
}

const ALLOWED_TYPES = {
  image: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  document: ['text/plain', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
}

const ALLOWED_EXTENSIONS = {
  image: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  document: ['.txt', '.pdf', '.doc', '.docx']
}

function getFileCategory(mimeType: string, fileName: string): 'image' | 'document' | null {
  const extension = path.extname(fileName).toLowerCase()
  
  if (ALLOWED_TYPES.image.includes(mimeType) && ALLOWED_EXTENSIONS.image.includes(extension)) {
    return 'image'
  }
  
  if (ALLOWED_TYPES.document.includes(mimeType) && ALLOWED_EXTENSIONS.document.includes(extension)) {
    return 'document'
  }
  
  return null
}

function validateFile(file: File): { valid: boolean; error?: string; category?: 'image' | 'document' } {
  const category = getFileCategory(file.type, file.name)
  
  if (!category) {
    return {
      valid: false,
      error: `Tipo de arquivo não suportado. Tipos aceitos: ${[...ALLOWED_EXTENSIONS.image, ...ALLOWED_EXTENSIONS.document].join(', ')}`
    }
  }
  
  const maxSize = MAX_FILE_SIZE[category]
  if (file.size > maxSize) {
    const maxSizeMB = maxSize / (1024 * 1024)
    return {
      valid: false,
      error: `Arquivo muito grande. Tamanho máximo para ${category === 'image' ? 'imagens' : 'documentos'}: ${maxSizeMB}MB`
    }
  }
  
  return { valid: true, category }
}

async function processTextFile(buffer: Buffer, fileName: string): Promise<string> {
  try {
    // Para arquivos .txt, simplesmente converter buffer para string
    if (fileName.endsWith('.txt')) {
      return buffer.toString('utf-8')
    }
    
    // Para outros tipos de documento, retornar informação básica
    // Em uma implementação completa, você usaria bibliotecas como pdf-parse, mammoth, etc.
    return `[Documento: ${fileName}] - Conteúdo do documento foi carregado para análise.`
  } catch (error) {
    throw new Error('Erro ao processar arquivo de texto')
  }
}

async function saveFile(buffer: Buffer, fileName: string): Promise<string> {
  try {
    // Criar diretório de uploads se não existir
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads')
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true })
    }
    
    // Gerar nome único para o arquivo
    const timestamp = Date.now()
    const extension = path.extname(fileName)
    const baseName = path.basename(fileName, extension)
    const uniqueFileName = `${baseName}_${timestamp}${extension}`
    
    const filePath = path.join(uploadsDir, uniqueFileName)
    await writeFile(filePath, buffer)
    
    // Retornar URL pública do arquivo
    return `/uploads/${uniqueFileName}`
  } catch (error) {
    throw new Error('Erro ao salvar arquivo')
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'Nenhum arquivo foi enviado' },
        { status: 400 }
      )
    }
    
    // Validar arquivo
    const validation = validateFile(file)
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      )
    }
    
    // Converter arquivo para buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    // Salvar arquivo
    const fileUrl = await saveFile(buffer, file.name)
    
    // Processar conteúdo baseado no tipo
    let content = ''
    let analysis = ''
    
    if (validation.category === 'document') {
      try {
        content = await processTextFile(buffer, file.name)
      } catch (error) {
        content = `[Documento: ${file.name}] - Arquivo carregado para análise.`
      }
    }
    
    // Preparar resposta
    const response = {
      success: true,
      file: {
        name: file.name,
        size: file.size,
        type: file.type,
        category: validation.category,
        url: fileUrl,
        content: content,
        uploadedAt: new Date().toISOString()
      }
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    logger.error('Erro no upload:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor durante o upload' },
      { status: 500 }
    )
  }
}

// Configuração para permitir uploads de arquivos
export const config = {
  api: {
    bodyParser: false,
  },
}
