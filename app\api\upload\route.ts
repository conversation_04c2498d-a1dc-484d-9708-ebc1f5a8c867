import { NextRequest, NextResponse } from "next/server"
import { writeFile, mkdir } from "fs/promises"
import { existsSync } from "fs"
import path from "path"
import { logger } from "@/lib/logger"

// Configurações de upload - APENAS IMAGENS
const MAX_FILE_SIZE = {
  image: 10 * 1024 * 1024, // 10MB para imagens
}

const ALLOWED_TYPES = {
  image: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
}

const ALLOWED_EXTENSIONS = {
  image: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
}

function getFileCategory(mimeType: string, fileName: string): 'image' | null {
  const extension = path.extname(fileName).toLowerCase()

  if (ALLOWED_TYPES.image.includes(mimeType) && ALLOWED_EXTENSIONS.image.includes(extension)) {
    return 'image'
  }

  return null
}

function validateFile(file: File): { valid: boolean; error?: string; category?: 'image' } {
  const category = getFileCategory(file.type, file.name)

  if (!category) {
    return {
      valid: false,
      error: `Tipo de arquivo não suportado. Apenas imagens são aceitas: ${ALLOWED_EXTENSIONS.image.join(', ')}`
    }
  }

  const maxSize = MAX_FILE_SIZE[category]
  if (file.size > maxSize) {
    const maxSizeMB = maxSize / (1024 * 1024)
    return {
      valid: false,
      error: `Imagem muito grande. Tamanho máximo: ${maxSizeMB}MB`
    }
  }

  return { valid: true, category }
}

// Função removida - não processamos mais documentos de texto

async function saveFile(buffer: Buffer, fileName: string): Promise<string> {
  try {
    // Criar diretório de uploads se não existir
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads')
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true })
    }
    
    // Gerar nome único para o arquivo
    const timestamp = Date.now()
    const extension = path.extname(fileName)
    const baseName = path.basename(fileName, extension)
    const uniqueFileName = `${baseName}_${timestamp}${extension}`
    
    const filePath = path.join(uploadsDir, uniqueFileName)
    await writeFile(filePath, buffer)
    
    // Retornar URL pública do arquivo
    return `/uploads/${uniqueFileName}`
  } catch (error) {
    throw new Error('Erro ao salvar arquivo')
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'Nenhum arquivo foi enviado' },
        { status: 400 }
      )
    }
    
    // Validar arquivo
    const validation = validateFile(file)
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      )
    }
    
    // Converter arquivo para buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    // Salvar arquivo
    const fileUrl = await saveFile(buffer, file.name)
    
    // Apenas imagens são processadas agora
    let content = ''

    // Para imagens, não há conteúdo de texto para extrair
    if (validation.category === 'image') {
      content = `[Imagem: ${file.name}] - Imagem carregada para análise visual.`
    }
    
    // Preparar resposta
    const response = {
      success: true,
      file: {
        name: file.name,
        size: file.size,
        type: file.type,
        category: validation.category,
        url: fileUrl,
        content: content,
        uploadedAt: new Date().toISOString()
      }
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    logger.error('Erro no upload:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor durante o upload' },
      { status: 500 }
    )
  }
}

// Configuração para permitir uploads de arquivos
export const config = {
  api: {
    bodyParser: false,
  },
}
