# 👤 Perfil do Usuário - Guia Completo

## 📋 Visão Geral

O sistema de Perfil do Usuário permite personalizar as respostas dos consultores de acordo com o contexto específico do seu negócio e preferências de comunicação. Isso garante que as orientações sejam mais relevantes e adequadas à sua realidade empresarial.

## 🔧 Como Configurar

### 1. Acesse as Configurações
- Clique no ícone ⚙️ (engrenagem) na interface principal
- Navegue até a seção "Perfil do Usuário"

### 2. Preencha os Campos Obrigatórios (*)

#### **Tipo de Negócio** *
Selecione a categoria que melhor descreve sua atividade:
- **Commerce/Retail**: Lojas, e-commerce, varejo
- **Industry/Manufacturing**: Indústria, manufatura, produção
- **Services**: Prestação de serviços, consultoria
- **Technology**: Desenvolvimento, TI, software
- **Healthcare**: Saúde, medicina, farmácia
- **Education**: Educação, ensino, treinamento
- **Other**: Outros (especifique no campo adicional)

#### **Tamanho da Empresa** *
- **Individual/Freelancer**: Profissional autônomo
- **Small Business (1-10 employees)**: Pequena empresa
- **Medium Business (11-50 employees)**: Média empresa
- **Large Business (50+ employees)**: Grande empresa

#### **Estilo de Comunicação** *
- **Formal/Professional**: Tom corporativo e protocolar
- **Casual/Friendly**: Linguagem descontraída e amigável
- **Technical/Detailed**: Abordagem técnica e detalhada
- **Simple/Concise**: Comunicação direta e objetiva

#### **Complexidade da Linguagem** *
- **Simple language**: Linguagem simples e acessível
- **Professional terminology**: Terminologia profissional
- **Technical jargon when appropriate**: Jargão técnico quando necessário

### 3. Campos Opcionais

#### **Especialização da Indústria**
Campo livre para detalhar sua área específica de atuação:
- Exemplos: "E-commerce de moda", "Consultoria em RH", "Desenvolvimento de apps móveis"

## 🎯 Como o Perfil Afeta as Respostas

### **Personalização Automática**
Quando você configura seu perfil, os consultores automaticamente:

1. **Adaptam o vocabulário** conforme sua preferência de complexidade
2. **Ajustam o tom** baseado no seu estilo de comunicação
3. **Focam em aspectos relevantes** para seu tipo e tamanho de negócio
4. **Consideram sua especialização** nas orientações específicas

### **Exemplo Prático**

**Sem Perfil:**
> "Para questões tributárias, recomendo consultar a legislação vigente..."

**Com Perfil (Small Business + Casual + Simple):**
> "Olá! Para sua pequena empresa, o mais importante é manter a contabilidade em dia. Vou explicar de forma simples: você precisa..."

**Com Perfil (Large Business + Formal + Technical):**
> "Considerando o porte de sua organização, recomendo implementar controles internos robustos conforme as diretrizes da Instrução Normativa..."

## 📊 Campos e Validações

### **Campos Obrigatórios**
- Tipo de Negócio
- Tamanho da Empresa  
- Estilo de Comunicação
- Complexidade da Linguagem
- Tipo de negócio "Other" requer especificação

### **Validação em Tempo Real**
- ✅ Campos preenchidos corretamente = botão "Salvar" habilitado
- ⚠️ Campos obrigatórios em falta = aviso de validação
- 🔴 Formato inválido = indicação visual de erro

## 💾 Persistência de Dados

### **Armazenamento Local**
- Dados salvos no `localStorage` do navegador
- Persistem entre sessões
- Carregados automaticamente ao abrir as configurações

### **Uso nas APIs**
- Perfil enviado automaticamente em todas as requisições de chat
- Consultores recebem contexto personalizado
- Respostas adaptadas em tempo real

## 🔄 Atualizações e Modificações

### **Alterando o Perfil**
1. Acesse as configurações novamente
2. Modifique os campos desejados
3. Clique em "Salvar Configurações"
4. Mudanças aplicadas imediatamente

### **Reset do Perfil**
Para limpar o perfil:
1. Limpe os campos manualmente
2. Ou use as ferramentas do desenvolvedor para limpar `localStorage`

## 🎨 Interface e Experiência

### **Design Intuitivo**
- Labels claros e descritivos
- Textos de ajuda para cada campo
- Validação visual em tempo real
- Feedback imediato de erros

### **Responsividade**
- Funciona em desktop e mobile
- Interface adaptável
- Campos organizados logicamente

## 🚀 Benefícios

### **Para o Usuário**
- ✅ Respostas mais relevantes
- ✅ Comunicação adequada ao contexto
- ✅ Economia de tempo
- ✅ Orientações práticas

### **Para os Consultores**
- ✅ Contexto rico para respostas
- ✅ Personalização automática
- ✅ Maior precisão nas orientações
- ✅ Melhor experiência do usuário

## 🔧 Implementação Técnica

### **Frontend**
- React hooks para gerenciamento de estado
- Validação em tempo real
- Persistência automática no localStorage

### **Backend**
- APIs recebem perfil do usuário
- System prompts personalizados
- Contexto injetado automaticamente

### **Integração**
- Funciona com todos os modelos de IA
- Compatible com OpenRouter API
- Suporte a modelos customizados

---

**💡 Dica:** Configure seu perfil uma vez e desfrute de respostas personalizadas em todas as suas interações com os consultores!
