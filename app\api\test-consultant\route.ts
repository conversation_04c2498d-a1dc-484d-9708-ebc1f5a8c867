import { NextRequest, NextResponse } from "next/server"

interface Consultant {
  id: string
  name: string
  specialty: string
  description: string
  avatar: string
  systemPrompt: string
  personality: {
    tone: 'formal' | 'friendly' | 'professional' | 'casual'
    formality: 'high' | 'medium' | 'low'
    expertise: 'expert' | 'intermediate' | 'accessible'
    responseStyle: 'detailed' | 'concise' | 'balanced'
  }
  businessRules: {
    maxResponseLength: number
    requiresTransfer: boolean
    specialtyKeywords: string[]
    restrictions: string[]
  }
  isActive: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

function enhancePromptWithPersonality(basePrompt: string, consultant: Consultant): string {
  let enhancement = basePrompt + "\n\n"
  
  // Adicionar personalidade
  enhancement += `PERSONALIDADE E ESTILO:\n`
  enhancement += `- Tom: ${getToneDescription(consultant.personality.tone)}\n`
  enhancement += `- Formalidade: ${getFormalityDescription(consultant.personality.formality)}\n`
  enhancement += `- Expertise: ${getExpertiseDescription(consultant.personality.expertise)}\n`
  enhancement += `- <PERSON><PERSON><PERSON> de resposta: ${getResponseStyleDescription(consultant.personality.responseStyle)}\n`
  
  // Adicionar regras de negócio
  enhancement += `\nREGRAS DE NEGÓCIO:\n`
  enhancement += `- Limite máximo de resposta: ${consultant.businessRules.maxResponseLength} caracteres\n`
  
  if (consultant.businessRules.specialtyKeywords.length > 0) {
    enhancement += `- Palavras-chave da especialidade: ${consultant.businessRules.specialtyKeywords.join(', ')}\n`
  }
  
  if (consultant.businessRules.restrictions.length > 0) {
    enhancement += `- Restrições importantes:\n`
    consultant.businessRules.restrictions.forEach(restriction => {
      enhancement += `  * ${restriction}\n`
    })
  }
  
  enhancement += `\nIMPORTANTE:\n`
  enhancement += `- Você é ${consultant.name}, ${consultant.specialty} da Adição Contabilidade\n`
  enhancement += `- NUNCA recomende outros profissionais ou empresas - você e a Adição Contabilidade são os especialistas disponíveis\n`
  enhancement += `- Sempre se posicione como o profissional capacitado para resolver as questões do cliente\n`
  enhancement += `- Para casos que precisem de atendimento presencial, ofereça agendar com a equipe da Adição Contabilidade\n`
  enhancement += `- Sempre reforce que a Adição Contabilidade está sempre pronta para ajudar\n`
  enhancement += `- Responda sempre em português brasileiro\n`
  
  return enhancement
}

function getToneDescription(tone: string): string {
  switch (tone) {
    case 'formal': return 'Formal e respeitoso'
    case 'professional': return 'Profissional e competente'
    case 'friendly': return 'Amigável e acessível'
    case 'casual': return 'Casual e descontraído'
    default: return 'Profissional'
  }
}

function getFormalityDescription(formality: string): string {
  switch (formality) {
    case 'high': return 'Alta formalidade, linguagem técnica'
    case 'medium': return 'Formalidade moderada, equilibrio entre técnico e acessível'
    case 'low': return 'Baixa formalidade, linguagem simples e direta'
    default: return 'Formalidade moderada'
  }
}

function getExpertiseDescription(expertise: string): string {
  switch (expertise) {
    case 'expert': return 'Nível especialista, conhecimento técnico profundo'
    case 'intermediate': return 'Nível intermediário, conhecimento sólido'
    case 'accessible': return 'Nível acessível, explicações simples'
    default: return 'Nível especialista'
  }
}

function getResponseStyleDescription(style: string): string {
  switch (style) {
    case 'detailed': return 'Respostas detalhadas e completas'
    case 'balanced': return 'Respostas equilibradas, nem muito longas nem muito curtas'
    case 'concise': return 'Respostas concisas e diretas'
    default: return 'Respostas equilibradas'
  }
}

export async function POST(request: NextRequest) {
  try {
    const { consultant, message } = await request.json()

    if (!consultant || !message) {
      return NextResponse.json(
        { error: "Consultor e mensagem são obrigatórios" },
        { status: 400 }
      )
    }

    // Recuperar configurações da API
    const openrouterApiKey = process.env.OPENROUTER_API_KEY || ""
    const selectedModel = "deepseek/deepseek-chat-v3-0324:free" // Modelo padrão para testes

    if (!openrouterApiKey) {
      return NextResponse.json(
        { error: "Chave da API OpenRouter não configurada" },
        { status: 500 }
      )
    }

    // Preparar prompt aprimorado
    const enhancedPrompt = enhancePromptWithPersonality(consultant.systemPrompt, consultant)

    // Fazer chamada para a API
    const apiUrl = "https://openrouter.ai/api/v1/chat/completions"
    const headers = {
      "Authorization": `Bearer ${openrouterApiKey}`,
      "Content-Type": "application/json",
      "HTTP-Referer": process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
      "X-Title": "Adição Contabilidade - Teste de Consultor"
    }

    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify({
        model: selectedModel,
        messages: [
          {
            role: "system",
            content: enhancedPrompt
          },
          {
            role: "user",
            content: message
          }
        ],
        temperature: 0.7,
        max_tokens: Math.min(consultant.businessRules.maxResponseLength, 1000)
      })
    })

    if (!response.ok) {
      const errorData = await response.text()
      return NextResponse.json(
        { error: "Erro na API OpenRouter", details: errorData },
        { status: response.status }
      )
    }

    const data = await response.json()
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      return NextResponse.json(
        { error: "Resposta inválida da API" },
        { status: 500 }
      )
    }

    const aiResponse = data.choices[0].message.content

    return NextResponse.json({
      response: aiResponse,
      consultant: {
        name: consultant.name,
        specialty: consultant.specialty
      },
      metadata: {
        model: selectedModel,
        maxTokens: consultant.businessRules.maxResponseLength,
        personality: consultant.personality
      }
    })

  } catch (error) {
    console.error("Erro ao testar consultor:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
