/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configuração de porta padrão
  env: {
    PORT: '3001',
  },

  // Configurações de build
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // Configurações de imagem
  images: {
    unoptimized: true,
    domains: ['localhost'],
  },

  // Configurações de produção
  productionBrowserSourceMaps: false,
  poweredByHeader: false,
  compress: true,

  // Remover logs de desenvolvimento
  logging: {
    fetches: {
      fullUrl: false,
    },
  },

  // Otimizações
  experimental: {
    // optimizeCss: true, // Desabilitado temporariamente
  },

  // Pacotes externos para server components
  serverExternalPackages: ['multer'],

  // Headers de segurança
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ]
  },

  // Configurações de output para produção
  output: 'standalone',

  // Configurações de webpack para produção
  webpack: (config, { dev, isServer }) => {
    if (!dev) {
      // Configurações específicas para produção
      config.optimization.minimize = true
      config.optimization.sideEffects = false
    }

    return config
  },
}

export default nextConfig
