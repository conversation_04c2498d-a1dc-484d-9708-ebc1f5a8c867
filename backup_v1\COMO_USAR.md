# 🚀 Como Usar o Backup backup_v1

## ⚡ Início <PERSON>

### 1. Instalar Dependências
```bash
cd backup_v1
pnpm install
```

### 2. Configurar API (Opcional)
```bash
# Copie o arquivo de exemplo
cp .env.example .env

# Edite e adicione sua chave da OpenRouter
# OPENROUTER_API_KEY=sk-or-v1-sua-chave-aqui
```

### 3. Executar
```bash
pnpm dev
```

### 4. Acessar
- Abra: http://localhost:3000
- Configure: Clique no ícone ⚙️ (engrenagem)

## 🔑 Configuração da Chave API

### Obter Chave OpenRouter
1. Visite: https://openrouter.ai
2. Crie uma conta
3. Gere uma chave API
4. Formato: `sk-or-v1-...`

### Configurar na Aplicação
1. Clique no ícone ⚙️ (configurações)
2. Cole sua chave no campo "Chave da API OpenRouter"
3. Selecione um modelo
4. <PERSON><PERSON> "Testar" para verificar
5. Clique "Salvar Configurações"

## 👥 Consultores Disponíveis

### 👨‍⚖️ Dr. Carlos Silva - Consultor Jurídico
- Especialista em direito empresarial
- Direito trabalhista e tributário
- Orientações legais práticas

### 👩‍💼 Dra. Ana Santos - Contadora
- Contabilidade empresarial
- Planejamento financeiro
- Gestão fiscal

### 👨‍💼 Dr. Roberto Lima - Auditor Fiscal
- Auditoria fiscal e compliance
- Conformidade tributária
- Controles internos

## 🤖 Modelos de IA

### 🆓 Gratuitos (50 req/dia)
- DeepSeek Chat V3
- Google Gemini Flash 1.5/2.0
- Meta Llama 3.1/3.3/4
- Mistral 7B Instruct
- Qwen 2.5 7B Instruct

### 💰 Pagos (Requer créditos)
- Claude 3/3.5 (Haiku, Sonnet, Opus)
- GPT-4/4o/5
- OpenAI o1
- Gemini Pro
- Grok 2

## 🛠️ Comandos Úteis

```bash
# Desenvolvimento
pnpm dev

# Build para produção
pnpm build

# Executar produção
pnpm start

# Verificar código
pnpm lint

# Instalar dependências
pnpm install

# Limpar cache
rm -rf .next node_modules
pnpm install
```

## 🔧 Solução de Problemas

### ❌ Erro "Chave da API inválida"
- Verifique se a chave começa com `sk-or-v1-`
- Confirme que a chave está ativa
- Verifique créditos na conta OpenRouter

### ❌ Erro "Port 3000 is in use"
- A aplicação tentará usar porta 3001 automaticamente
- Ou pare outros serviços na porta 3000

### ❌ Erro de instalação
```bash
# Limpe e reinstale
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

### ❌ Erro de compilação
```bash
# Limpe cache do Next.js
rm -rf .next
pnpm dev
```

## 📱 Funcionalidades

### ✅ Chat Inteligente
- Conversas contextuais
- Histórico de mensagens
- Respostas especializadas

### ✅ Configurações Avançadas
- Múltiplos modelos de IA
- Teste de configuração
- URLs customizadas

### ✅ Interface Moderna
- Design responsivo
- Tema claro/escuro
- Componentes acessíveis

### ✅ Validação Robusta
- Formato de chave API
- Tratamento de erros
- Feedback detalhado

## 🎯 Dicas de Uso

1. **Teste primeiro**: Use modelos gratuitos para testar
2. **Configure bem**: Valide a chave antes de usar
3. **Monitore créditos**: Acompanhe uso na OpenRouter
4. **Seja específico**: Perguntas claras = respostas melhores
5. **Use contexto**: Mencione informações relevantes

---

**🎉 Divirta-se usando o backup_v1! 🎉**
