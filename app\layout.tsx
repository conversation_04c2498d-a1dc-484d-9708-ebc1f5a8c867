import type { Metada<PERSON> } from 'next'
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import { Analytics } from '@vercel/analytics/next'
import { Toaster } from 'sonner'
import { setupProductionLogging } from '@/lib/logger'
import './globals.css'

// Configurar logging para produção
if (typeof window === 'undefined') {
  setupProductionLogging()
}

export const metadata: Metadata = {
  title: 'Adição Contabilidade - Consultoria Especializada',
  description: 'Sistema de consultoria especializada em contabilidade, auditoria e jurídico com IA avançada. Adição Contabilidade - Soluções profissionais para seu negócio.',
  keywords: 'contabilidade, auditoria, jurídico, consultoria, IA, Adição Contabilidade',
  authors: [{ name: 'Adição Contabilidade' }],
  creator: '<PERSON><PERSON><PERSON> Contabilidade',
  publisher: '<PERSON><PERSON>ção Contabilidade',
  robots: 'index, follow',
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#1e40af',
  openGraph: {
    title: 'Adição Contabilidade - Consultoria Especializada',
    description: 'Sistema de consultoria especializada com IA avançada',
    type: 'website',
    locale: 'pt_BR',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="pt-BR">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#1e40af" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable}`}>
        {children}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'hsl(var(--background))',
              color: 'hsl(var(--foreground))',
              border: '1px solid hsl(var(--border))',
            },
          }}
        />
        {process.env.NODE_ENV === 'production' && <Analytics />}
      </body>
    </html>
  )
}
