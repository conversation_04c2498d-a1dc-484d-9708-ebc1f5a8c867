import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { message, model, customUrl, apiKey } = await request.json()

    console.log("[v0] Teste LLM - Dados recebidos:", { message, model, customUrl, apiKey: apiKey ? "***" : "não fornecida" })

    if (!message || !model) {
      return NextResponse.json({ error: "Mensagem e modelo são obrigatórios" }, { status: 400 })
    }

    const apiUrl = model === "custom" && customUrl ? customUrl : "https://openrouter.ai/api/v1/chat/completions"

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    if (model !== "custom") {
      // Usa a chave fornecida pelo usuário ou a variável de ambiente como fallback
      const openrouterKey = apiKey || process.env.OPENROUTER_API_KEY

      if (!openrouterKey) {
        return NextResponse.json({ error: "Chave da API OpenRouter é obrigatória para modelos não customizados" }, { status: 400 })
      }

      headers["Authorization"] = `Bearer ${openrouterKey}`
      headers["HTTP-Referer"] = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"
      headers["X-Title"] = "Consultoria Especializada"
    }

    console.log("[v0] Fazendo requisição para:", apiUrl)
    console.log("[v0] Headers:", headers)

    const requestBody = {
      model: model === "custom" ? "gpt-3.5-turbo" : model,
      messages: [
        {
          role: "system",
          content: "Você é um assistente útil. Responda de forma concisa e clara.",
        },
        {
          role: "user",
          content: message,
        },
      ],
      max_tokens: 500,
      temperature: 0.7,
    }

    console.log("[v0] Body da requisição:", JSON.stringify(requestBody, null, 2))

    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
    })

    console.log("[v0] Status da resposta:", response.status)
    console.log("[v0] Headers da resposta:", Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorData = await response.text()
      console.error("[v0] Erro da API (texto):", errorData)
      return NextResponse.json({ error: `Erro da API: ${response.status} - ${errorData}` }, { status: 500 })
    }

    const responseText = await response.text()
    console.log("[v0] Resposta bruta:", responseText)

    let data
    try {
      data = JSON.parse(responseText)
      console.log("[v0] JSON parseado com sucesso:", data)
    } catch (parseError) {
      console.error("[v0] Erro ao fazer parse do JSON:", parseError)
      console.error("[v0] Conteúdo que causou erro:", responseText.substring(0, 500))
      return NextResponse.json({ error: "Resposta inválida da API" }, { status: 500 })
    }

    const assistantMessage = data.choices?.[0]?.message?.content || "Sem resposta"
    console.log("[v0] Mensagem extraída:", assistantMessage)

    return NextResponse.json({ response: assistantMessage })
  } catch (error) {
    console.error("[v0] Erro no teste da LLM:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
