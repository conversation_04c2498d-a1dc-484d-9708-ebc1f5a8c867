import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { message, model, customUrl, apiKey } = await request.json()

    console.log("[v0] Teste LLM - Dados recebidos:", { message, model, customUrl, apiKey: apiKey ? "***" : "não fornecida" })

    if (!message || !model) {
      return NextResponse.json({ error: "Mensagem e modelo são obrigatórios" }, { status: 400 })
    }

    const apiUrl = model === "custom" && customUrl ? customUrl : "https://openrouter.ai/api/v1/chat/completions"

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    if (model !== "custom") {
      // Usa a chave fornecida pelo usuário ou a variável de ambiente como fallback
      const openrouterKey = apiKey || process.env.OPENROUTER_API_KEY

      if (!openrouterKey) {
        return NextResponse.json({
          error: "Chave da API OpenRouter é obrigatória para todos os modelos. Por favor, insira sua chave nas configurações."
        }, { status: 400 })
      }

      // Validar formato da chave da API
      if (!openrouterKey.startsWith("sk-or-v1-")) {
        return NextResponse.json({
          error: "Formato da chave da API inválido. A chave deve começar com 'sk-or-v1-'"
        }, { status: 400 })
      }

      headers["Authorization"] = `Bearer ${openrouterKey}`
      headers["HTTP-Referer"] = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3001"
      headers["X-Title"] = "Consultoria Especializada"
    }

    console.log("[v0] Fazendo requisição para:", apiUrl)
    console.log("[v0] Headers:", headers)

    const requestBody = {
      model: model === "custom" ? "gpt-3.5-turbo" : model,
      messages: [
        {
          role: "system",
          content: "Você é um assistente útil. Responda de forma concisa e clara.",
        },
        {
          role: "user",
          content: message,
        },
      ],
      max_tokens: 500,
      temperature: 0.7,
    }

    console.log("[v0] Body da requisição:", JSON.stringify(requestBody, null, 2))

    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
    })

    console.log("[v0] Status da resposta:", response.status)
    console.log("[v0] Headers da resposta:", Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorData = await response.text()
      console.error("[v0] Erro da API (texto):", errorData)

      let errorMessage = "Erro desconhecido da API"

      try {
        const errorJson = JSON.parse(errorData)
        const apiError = errorJson.error?.message || errorJson.message || errorData

        // Tratar erros específicos da OpenRouter
        if (response.status === 401) {
          if (apiError.includes("User not found") || apiError.includes("Invalid API key")) {
            errorMessage = "Chave da API inválida. Verifique se sua chave OpenRouter está correta e ativa."
          } else {
            errorMessage = "Erro de autenticação. Verifique sua chave da API."
          }
        } else if (response.status === 402) {
          errorMessage = "Créditos insuficientes na sua conta OpenRouter."
        } else if (response.status === 429) {
          errorMessage = "Limite de requisições excedido. Tente novamente em alguns minutos."
        } else if (response.status === 400) {
          errorMessage = `Erro na requisição: ${apiError}`
        } else {
          errorMessage = `Erro da API (${response.status}): ${apiError}`
        }
      } catch (parseError) {
        errorMessage = `Erro da API (${response.status}): ${errorData}`
      }

      return NextResponse.json({ error: errorMessage }, { status: response.status })
    }

    const responseText = await response.text()
    console.log("[v0] Resposta bruta:", responseText)

    let data
    try {
      data = JSON.parse(responseText)
      console.log("[v0] JSON parseado com sucesso:", data)
    } catch (parseError) {
      console.error("[v0] Erro ao fazer parse do JSON:", parseError)
      console.error("[v0] Conteúdo que causou erro:", responseText.substring(0, 500))
      return NextResponse.json({ error: "Resposta inválida da API" }, { status: 500 })
    }

    const assistantMessage = data.choices?.[0]?.message?.content || "Sem resposta"
    console.log("[v0] Mensagem extraída:", assistantMessage)

    return NextResponse.json({ response: assistantMessage })
  } catch (error) {
    console.error("[v0] Erro no teste da LLM:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
