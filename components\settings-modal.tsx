"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { X, Send, Loader2, Save } from "lucide-react"

interface SettingsModalProps {
  isOpen: boolean
  onClose: () => void
  onModelChange: (model: string) => void
}

const availableModels = [
  // Modelos Gratuitos (ordenados alfabeticamente)
  { id: "agentica-org/deepcoder-14b-preview:free", name: "DeepCoder 14B Preview (Free)", category: "free" },
  { id: "deepseek/deepseek-chat-v3-0324:free", name: "DeepSeek Chat V3 (Free)", category: "free" },
  { id: "deepseek/deepseek-r1:free", name: "DeepSeek R1 (Free)", category: "free" },
  { id: "deepseek/deepseek-r1-0528-qwen3-8b:free", name: "DeepSeek R1 0528 Qwen3 8B (Free)", category: "free" },
  { id: "google/gemini-flash-1.5:free", name: "Gemini Flash 1.5 (Free)", category: "free" },
  { id: "google/gemini-flash-2.0:free", name: "Gemini Flash 2.0 (Free)", category: "free" },
  { id: "meta-llama/llama-3.1-8b-instruct:free", name: "Llama 3.1 8B Instruct (Free)", category: "free" },
  { id: "meta-llama/llama-3.3-8b-instruct:free", name: "Llama 3.3 8B Instruct (Free)", category: "free" },
  { id: "meta-llama/llama-4-maverick:free", name: "Llama 4 Maverick (Free)", category: "free" },
  { id: "meta-llama/llama-4-scout:free", name: "Llama 4 Scout (Free)", category: "free" },
  { id: "microsoft/mai-ds-r1:free", name: "Microsoft MAI DS R1 (Free)", category: "free" },
  { id: "mistralai/devstral-small:free", name: "Mistral Devstral Small (Free)", category: "free" },
  { id: "mistralai/mistral-7b-instruct:free", name: "Mistral 7B Instruct (Free)", category: "free" },
  { id: "qwen/qwen-2.5-7b-instruct:free", name: "Qwen 2.5 7B Instruct (Free)", category: "free" },
  { id: "qwen/qwen3-235b-a22b:free", name: "Qwen 3 235B A22B (Free)", category: "free" },

  // Modelos Pagos (ordenados alfabeticamente)
  { id: "anthropic/claude-3-haiku", name: "Claude 3 Haiku", category: "paid" },
  { id: "anthropic/claude-3-opus", name: "Claude 3 Opus", category: "paid" },
  { id: "anthropic/claude-3-sonnet", name: "Claude 3 Sonnet", category: "paid" },
  { id: "anthropic/claude-3.5-haiku", name: "Claude 3.5 Haiku", category: "paid" },
  { id: "anthropic/claude-3.5-sonnet", name: "Claude 3.5 Sonnet", category: "paid" },
  { id: "cohere/command-r", name: "Cohere Command R", category: "paid" },
  { id: "cohere/command-r-plus", name: "Cohere Command R Plus", category: "paid" },
  { id: "deepseek/deepseek-chat", name: "DeepSeek Chat", category: "paid" },
  { id: "deepseek/deepseek-coder", name: "DeepSeek Coder", category: "paid" },
  { id: "google/gemini-pro-1.5", name: "Gemini Pro 1.5", category: "paid" },
  { id: "google/gemini-pro-2.0", name: "Gemini Pro 2.0", category: "paid" },
  { id: "meta-llama/llama-3.1-70b-instruct", name: "Llama 3.1 70B Instruct", category: "paid" },
  { id: "meta-llama/llama-3.1-405b-instruct", name: "Llama 3.1 405B Instruct", category: "paid" },
  { id: "meta-llama/llama-3.3-70b-instruct", name: "Llama 3.3 70B Instruct", category: "paid" },
  { id: "mistralai/mistral-large", name: "Mistral Large", category: "paid" },
  { id: "mistralai/mistral-medium", name: "Mistral Medium", category: "paid" },
  { id: "mistralai/mistral-small", name: "Mistral Small", category: "paid" },
  { id: "openai/gpt-3.5-turbo", name: "GPT-3.5 Turbo", category: "paid" },
  { id: "openai/gpt-4", name: "GPT-4", category: "paid" },
  { id: "openai/gpt-4-turbo", name: "GPT-4 Turbo", category: "paid" },
  { id: "openai/gpt-4o", name: "GPT-4o", category: "paid" },
  { id: "openai/gpt-4o-mini", name: "GPT-4o Mini", category: "paid" },
  { id: "openai/gpt-5-chat", name: "GPT-5 Chat", category: "paid" },
  { id: "openai/o1", name: "OpenAI o1", category: "paid" },
  { id: "openai/o1-mini", name: "OpenAI o1 Mini", category: "paid" },
  { id: "openai/o1-preview", name: "OpenAI o1 Preview", category: "paid" },
  { id: "perplexity/llama-3.1-sonar-large-128k-online", name: "Perplexity Llama 3.1 Sonar Large", category: "paid" },
  { id: "qwen/qwen-2.5-72b-instruct", name: "Qwen 2.5 72B Instruct", category: "paid" },
  { id: "x-ai/grok-2", name: "Grok 2", category: "paid" },
  { id: "x-ai/grok-2-mini", name: "Grok 2 Mini", category: "paid" },

  // Modelo Customizado
  { id: "custom", name: "Modelo Customizado", category: "custom" },
]

export function SettingsModal({ isOpen, onClose, onModelChange }: SettingsModalProps) {
  const [selectedModel, setSelectedModel] = useState("deepseek/deepseek-chat-v3-0324:free")
  const [customUrl, setCustomUrl] = useState("")
  const [openrouterApiKey, setOpenrouterApiKey] = useState("")
  const [testMessage, setTestMessage] = useState("")
  const [testResponse, setTestResponse] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const savedModel = localStorage.getItem("selectedModel")
    const savedCustomUrl = localStorage.getItem("customUrl")
    const savedApiKey = localStorage.getItem("openrouterApiKey")
    if (savedModel) {
      setSelectedModel(savedModel)
    }
    if (savedCustomUrl) {
      setCustomUrl(savedCustomUrl)
    }
    if (savedApiKey) {
      setOpenrouterApiKey(savedApiKey)
    }
  }, [])

  const handleSaveSettings = () => {
    localStorage.setItem("selectedModel", selectedModel)
    localStorage.setItem("openrouterApiKey", openrouterApiKey)
    if (selectedModel === "custom") {
      localStorage.setItem("customUrl", customUrl)
    }
    onModelChange(selectedModel)
    onClose()
  }

  const handleTestLLM = async () => {
    if (!testMessage.trim()) return

    setIsLoading(true)
    setTestResponse("")

    try {
      const response = await fetch("/api/test-llm", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: testMessage,
          model: selectedModel,
          customUrl: selectedModel === "custom" ? customUrl : undefined,
          apiKey: openrouterApiKey,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        // Usar a mensagem de erro específica da API
        setTestResponse(`❌ Erro (${response.status}): ${data.error || "Erro desconhecido"}`)
        return
      }

      setTestResponse(`✅ Teste bem-sucedido!\n\nResposta: ${data.response}`)
    } catch (error) {
      console.error("Erro ao testar LLM:", error)
      setTestResponse(`❌ Erro de conexão: ${(error as Error).message}`)
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  const freeModels = availableModels.filter((model) => model.category === "free")
  const paidModels = availableModels.filter((model) => model.category === "paid")
  const customModels = availableModels.filter((model) => model.category === "custom")

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold">Configurações</h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* API Key Configuration */}
          <div className="space-y-4 mb-6">
            <div>
              <Label htmlFor="openrouter-api-key">Chave da API OpenRouter</Label>
              <Input
                id="openrouter-api-key"
                type="password"
                placeholder="sk-or-v1-..."
                value={openrouterApiKey}
                onChange={(e) => setOpenrouterApiKey(e.target.value)}
                className={`mt-1 ${openrouterApiKey && !openrouterApiKey.startsWith("sk-or-v1-") ? "border-red-500" : ""}`}
              />
              <div className="text-xs mt-1 space-y-1">
                <p className="text-muted-foreground">
                  ⚠️ <strong>Chave obrigatória:</strong> Todos os modelos (incluindo gratuitos) requerem uma chave da API OpenRouter
                </p>
                <p className="text-muted-foreground">
                  🆓 Modelos gratuitos: 50 requisições/dia | 💰 Modelos pagos: Conforme créditos
                </p>
                {openrouterApiKey && !openrouterApiKey.startsWith("sk-or-v1-") && (
                  <p className="text-red-500">
                    ⚠️ Formato inválido. A chave deve começar com "sk-or-v1-"
                  </p>
                )}
                <p className="text-muted-foreground">
                  📝 Obtenha sua chave em: <a href="https://openrouter.ai" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">openrouter.ai</a>
                </p>
              </div>
            </div>
          </div>

          {/* Model Selection */}
          <div className="space-y-4 mb-6">
            <div>
              <Label htmlFor="model-select">Modelo LLM</Label>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um modelo" />
                </SelectTrigger>
                <SelectContent className="max-h-60">
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-muted/50">
                    Modelos Gratuitos
                  </div>
                  {freeModels.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.name}
                    </SelectItem>
                  ))}

                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-muted/50 mt-2">
                    Modelos Pagos
                  </div>
                  {paidModels.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.name}
                    </SelectItem>
                  ))}

                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-muted/50 mt-2">
                    Personalizado
                  </div>
                  {customModels.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedModel === "custom" && (
              <div>
                <Label htmlFor="custom-url">URL da API Customizada</Label>
                <Input
                  id="custom-url"
                  type="url"
                  placeholder="https://api.exemplo.com/v1/chat/completions"
                  value={customUrl}
                  onChange={(e) => setCustomUrl(e.target.value)}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Insira a URL completa da API que seja compatível com o formato OpenAI
                </p>
              </div>
            )}
          </div>

          {/* Test LLM */}
          <div className="space-y-4 mb-6">
            <Label>Testar LLM</Label>
            <div className="space-y-3">
              <Textarea
                placeholder="Digite uma mensagem para testar..."
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                rows={3}
              />
              <Button
                onClick={handleTestLLM}
                disabled={
                  !testMessage.trim() ||
                  isLoading ||
                  (selectedModel === "custom" && !customUrl.trim()) ||
                  (!openrouterApiKey || !openrouterApiKey.startsWith("sk-or-v1-"))
                }
                className="w-full bg-transparent"
                variant="outline"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Testando...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Testar
                  </>
                )}
              </Button>
            </div>

            {/* Test Response */}
            {testResponse && (
              <div className="mt-4">
                <Label>Resposta:</Label>
                <div className="mt-2 p-3 bg-muted rounded-md text-sm max-h-32 overflow-y-auto">{testResponse}</div>
              </div>
            )}
          </div>

          {/* Save Button */}
          <Button
            onClick={handleSaveSettings}
            className="w-full"
            disabled={
              (selectedModel === "custom" && !customUrl.trim()) ||
              (!openrouterApiKey || !openrouterApiKey.startsWith("sk-or-v1-"))
            }
          >
            <Save className="h-4 w-4 mr-2" />
            Salvar Configurações
          </Button>
        </div>
      </Card>
    </div>
  )
}
