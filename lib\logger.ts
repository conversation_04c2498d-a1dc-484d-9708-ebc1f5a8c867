// Utilitário de logging para produção
// ===================================

const isProduction = process.env.NODE_ENV === 'production'
const isLoggingDisabled = process.env.DISABLE_CONSOLE_LOGS === 'true'

// Níveis de log
type LogLevel = 'debug' | 'info' | 'warn' | 'error'

const logLevels: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
}

const currentLogLevel = (process.env.LOG_LEVEL as LogLevel) || (isProduction ? 'error' : 'debug')

class Logger {
  private shouldLog(level: LogLevel): boolean {
    if (isLoggingDisabled) return false
    return logLevels[level] >= logLevels[currentLogLevel]
  }

  debug(...args: any[]) {
    if (this.shouldLog('debug')) {
      console.debug('[DEBUG]', ...args)
    }
  }

  info(...args: any[]) {
    if (this.shouldLog('info')) {
      console.info('[INFO]', ...args)
    }
  }

  warn(...args: any[]) {
    if (this.shouldLog('warn')) {
      console.warn('[WARN]', ...args)
    }
  }

  error(...args: any[]) {
    if (this.shouldLog('error')) {
      console.error('[ERROR]', ...args)
    }
  }

  // Método especial para logs de desenvolvimento
  dev(...args: any[]) {
    if (!isProduction) {
      console.log('[DEV]', ...args)
    }
  }
}

// Instância singleton do logger
export const logger = new Logger()

// Função para desabilitar console.log em produção
export function setupProductionLogging() {
  if (isProduction && isLoggingDisabled) {
    // Sobrescrever métodos de console em produção
    console.log = () => {}
    console.debug = () => {}
    console.info = () => {}
    
    // Manter apenas warn e error
    const originalWarn = console.warn
    const originalError = console.error
    
    console.warn = (...args: any[]) => {
      if (logLevels.warn >= logLevels[currentLogLevel]) {
        originalWarn('[WARN]', ...args)
      }
    }
    
    console.error = (...args: any[]) => {
      if (logLevels.error >= logLevels[currentLogLevel]) {
        originalError('[ERROR]', ...args)
      }
    }
  }
}

// Função para logs condicionais
export function conditionalLog(condition: boolean, ...args: any[]) {
  if (condition && !isProduction) {
    console.log(...args)
  }
}

// Função para logs de performance
export function performanceLog(label: string, fn: () => void) {
  if (!isProduction) {
    console.time(label)
    fn()
    console.timeEnd(label)
  } else {
    fn()
  }
}

export default logger
