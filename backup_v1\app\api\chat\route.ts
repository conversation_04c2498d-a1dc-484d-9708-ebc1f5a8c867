import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { message, systemPrompt, conversationHistory, consultorName, model, customUrl, apiKey } = await request.json()

    const apiUrl = model === "custom" && customUrl ? customUrl : "https://openrouter.ai/api/v1/chat/completions"

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    if (model !== "custom") {
      // Usa a chave fornecida pelo usuário ou a variável de ambiente como fallback
      const openrouterKey = apiKey || process.env.OPENROUTER_API_KEY

      if (!openrouterKey) {
        return NextResponse.json({
          error: "Chave da API OpenRouter é obrigatória para todos os modelos. Por favor, insira sua chave nas configurações."
        }, { status: 400 })
      }

      // Validar formato da chave da API
      if (!openrouterKey.startsWith("sk-or-v1-")) {
        return NextResponse.json({
          error: "Formato da chave da API inválido. A chave deve começar com 'sk-or-v1-'"
        }, { status: 400 })
      }

      headers["Authorization"] = `Bearer ${openrouterKey}`
      headers["HTTP-Referer"] = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3001"
      headers["X-Title"] = "Consultoria Especializada"
    }

    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify({
        model: model === "custom" ? "gpt-3.5-turbo" : model || "deepseek/deepseek-chat-v3-0324:free", // Handle custom model naming
        messages: [
          {
            role: "system",
            content: `${systemPrompt}

Histórico da conversa recente:
${conversationHistory}

Instruções adicionais:
- Responda sempre em português brasileiro
- Seja profissional, mas acessível
- Mantenha respostas concisas e práticas
- Se necessário, peça mais detalhes para dar uma orientação mais precisa
- Sempre considere o contexto das mensagens anteriores`,
          },
          {
            role: "user",
            content: message,
          },
        ],
        temperature: 0.7,
        max_tokens: 1000,
      }),
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error("Erro da API:", errorData)

      let errorMessage = "Erro ao processar solicitação"

      try {
        const errorJson = JSON.parse(errorData)
        const apiError = errorJson.error?.message || errorJson.message || errorData

        // Tratar erros específicos da OpenRouter
        if (response.status === 401) {
          if (apiError.includes("User not found") || apiError.includes("Invalid API key")) {
            errorMessage = "Chave da API inválida. Verifique se sua chave OpenRouter está correta e ativa."
          } else {
            errorMessage = "Erro de autenticação. Verifique sua chave da API."
          }
        } else if (response.status === 402) {
          errorMessage = "Créditos insuficientes na sua conta OpenRouter."
        } else if (response.status === 429) {
          errorMessage = "Limite de requisições excedido. Tente novamente em alguns minutos."
        } else if (response.status === 400) {
          errorMessage = `Erro na requisição: ${apiError}`
        } else {
          errorMessage = `Erro da API: ${apiError}`
        }
      } catch (parseError) {
        errorMessage = `Erro da API (${response.status}): ${errorData}`
      }

      return NextResponse.json({ error: errorMessage }, { status: response.status })
    }

    const data = await response.json()
    const aiResponse = data.choices[0]?.message?.content || "Desculpe, não consegui processar sua solicitação."

    return NextResponse.json({ response: aiResponse })
  } catch (error) {
    console.error("Erro no endpoint de chat:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
