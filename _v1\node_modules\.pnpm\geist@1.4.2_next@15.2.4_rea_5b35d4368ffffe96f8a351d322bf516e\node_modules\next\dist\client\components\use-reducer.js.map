{"version": 3, "sources": ["../../../src/client/components/use-reducer.ts"], "sourcesContent": ["import type { Dispatch } from 'react'\nimport React, { use, useCallback } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from '../../shared/lib/router/action-queue'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\nimport { useSyncDevRenderIndicator } from './react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator'\n\nexport function useUnwrapState(state: ReducerState): AppRouterState {\n  // reducer actions can be async, so sometimes we need to suspend until the state is resolved\n  if (isThenable(state)) {\n    const result = use(state)\n    return result\n  }\n\n  return state\n}\n\nexport function useReducer(\n  actionQueue: AppRouterActionQueue\n): [ReducerState, Dispatch<ReducerActions>] {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n  const syncDevRenderIndicator = useSyncDevRenderIndicator()\n\n  const dispatch = useCallback(\n    (action: ReducerActions) => {\n      syncDevRenderIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    },\n    [actionQueue, syncDevRenderIndicator]\n  )\n\n  return [state, dispatch]\n}\n"], "names": ["useReducer", "useUnwrapState", "state", "isThenable", "result", "use", "actionQueue", "setState", "React", "useState", "syncDevRenderIndicator", "useSyncDevRenderIndicator", "dispatch", "useCallback", "action"], "mappings": ";;;;;;;;;;;;;;;IAqBgBA,UAAU;eAAVA;;IAVAC,cAAc;eAAdA;;;;iEAVwB;4BACb;2CAOe;AAEnC,SAASA,eAAeC,KAAmB;IAChD,4FAA4F;IAC5F,IAAIC,IAAAA,sBAAU,EAACD,QAAQ;QACrB,MAAME,SAASC,IAAAA,UAAG,EAACH;QACnB,OAAOE;IACT;IAEA,OAAOF;AACT;AAEO,SAASF,WACdM,WAAiC;IAEjC,MAAM,CAACJ,OAAOK,SAAS,GAAGC,cAAK,CAACC,QAAQ,CAAeH,YAAYJ,KAAK;IACxE,MAAMQ,yBAAyBC,IAAAA,oDAAyB;IAExD,MAAMC,WAAWC,IAAAA,kBAAW,EAC1B,CAACC;QACCJ,uBAAuB;YACrBJ,YAAYM,QAAQ,CAACE,QAAQP;QAC/B;IACF,GACA;QAACD;QAAaI;KAAuB;IAGvC,OAAO;QAACR;QAAOU;KAAS;AAC1B"}