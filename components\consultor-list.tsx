"use client"

import { useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronRight, MessageCircle, Settings, Shield } from "lucide-react"
import { SettingsModal } from "@/components/settings-modal"
import type { Consultor } from "@/app/page"

interface ConsultorListProps {
  consultores: Consultor[]
  onSelectConsultor: (consultor: Consultor) => void
  onModelChange: (model: string) => void
  onOpenAdmin?: () => void
}

export function ConsultorList({ consultores, onSelectConsultor, onModelChange, onOpenAdmin }: ConsultorListProps) {
  const [showSettings, setShowSettings] = useState(false)

  return (
    <div className="flex flex-col h-screen">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MessageCircle className="h-6 w-6" />
            <div>
              <h1 className="text-lg font-semibold">Consultoria Especializada</h1>
              <p className="text-sm opacity-90">Escolha seu consultor</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* {onOpenAdmin && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onOpenAdmin}
                className="text-primary-foreground hover:bg-primary-foreground/10"
                title="Painel de Administração"
              >
                <Shield className="h-5 w-5" />
              </Button>
            )} */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(true)}
              className="text-primary-foreground hover:bg-primary-foreground/10"
              title="Configurações"
            >
              <Settings className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Consultores List */}
      <div className="flex-1 overflow-y-auto bg-muted/30">
        <div className="p-4 space-y-3">
          {consultores.map((consultor) => (
            <Card
              key={consultor.id}
              className="p-4 cursor-pointer hover:bg-accent/5 transition-colors border-border/50"
              onClick={() => onSelectConsultor(consultor)}
            >
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={consultor.avatar || "/placeholder.svg"} alt={consultor.name} />
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {consultor.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-foreground truncate">{consultor.name}</h3>
                    <ChevronRight className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                  </div>
                  <p className="text-sm font-medium text-accent">{consultor.specialty}</p>
                  <p className="text-sm text-muted-foreground truncate">{consultor.description}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 bg-card border-t border-border">
        <p className="text-xs text-muted-foreground text-center">Consultoria profissional disponível 24/7</p>
      </div>

      <SettingsModal isOpen={showSettings} onClose={() => setShowSettings(false)} onModelChange={onModelChange} />
    </div>
  )
}
