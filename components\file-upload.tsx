"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Paperclip, X, FileText, Image, Upload, Loader2 } from "lucide-react"
import { toast } from "sonner"

interface UploadedFile {
  name: string
  size: number
  type: string
  category: 'image' | 'document'
  url: string
  content?: string
  uploadedAt: string
}

interface FileUploadProps {
  onFileUploaded: (file: UploadedFile) => void
  disabled?: boolean
}

export function FileUpload({ onFileUploaded, disabled }: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return

    const file = files[0]
    await uploadFile(file)
  }

  const uploadFile = async (file: File) => {
    setIsUploading(true)
    
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro no upload')
      }

      if (result.success) {
        onFileUploaded(result.file)
        toast.success(`Arquivo "${file.name}" enviado com sucesso!`)
      }
    } catch (error) {
      console.error('Erro no upload:', error)
      toast.error(error instanceof Error ? error.message : 'Erro ao enviar arquivo')
    } finally {
      setIsUploading(false)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (disabled || isUploading) return
    
    const files = e.dataTransfer.files
    handleFileSelect(files)
  }

  const openFileDialog = () => {
    if (disabled || isUploading) return
    fileInputRef.current?.click()
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="relative">
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        accept=".txt,.pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.webp"
        onChange={(e) => handleFileSelect(e.target.files)}
        disabled={disabled || isUploading}
      />
      
      <Button
        variant="ghost"
        size="sm"
        onClick={openFileDialog}
        disabled={disabled || isUploading}
        className="h-8 w-8 p-0"
        title="Anexar arquivo"
      >
        {isUploading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Paperclip className="h-4 w-4" />
        )}
      </Button>

      {/* Área de drag and drop (opcional, pode ser ativada quando necessário) */}
      {dragActive && (
        <div
          className="fixed inset-0 z-50 bg-black/20 flex items-center justify-center"
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Card className="w-96 h-48 border-2 border-dashed border-primary">
            <CardContent className="flex flex-col items-center justify-center h-full">
              <Upload className="h-12 w-12 text-primary mb-4" />
              <p className="text-lg font-medium">Solte o arquivo aqui</p>
              <p className="text-sm text-muted-foreground">
                Documentos (PDF, DOC, TXT) até 5MB
              </p>
              <p className="text-sm text-muted-foreground">
                Imagens (JPG, PNG, GIF) até 10MB
              </p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

interface FilePreviewProps {
  file: UploadedFile
  onRemove?: () => void
  showRemove?: boolean
}

export function FilePreview({ file, onRemove, showRemove = false }: FilePreviewProps) {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = () => {
    if (file.category === 'image') {
      return <Image className="h-4 w-4" />
    }
    return <FileText className="h-4 w-4" />
  }

  return (
    <Card className="w-full max-w-sm">
      <CardContent className="p-3">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-1">
            {getFileIcon()}
          </div>
          
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate" title={file.name}>
              {file.name}
            </p>
            <p className="text-xs text-muted-foreground">
              {formatFileSize(file.size)} • {file.category === 'image' ? 'Imagem' : 'Documento'}
            </p>
            
            {file.category === 'image' && file.url && (
              <div className="mt-2">
                <img
                  src={file.url}
                  alt={file.name}
                  className="max-w-full h-20 object-cover rounded border"
                />
              </div>
            )}
            
            {file.content && file.category === 'document' && (
              <div className="mt-2 p-2 bg-muted rounded text-xs">
                <p className="line-clamp-3">{file.content.substring(0, 150)}...</p>
              </div>
            )}
          </div>
          
          {showRemove && onRemove && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onRemove}
              className="h-6 w-6 p-0 flex-shrink-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
