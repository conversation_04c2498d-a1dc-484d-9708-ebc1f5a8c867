# Consultant Chat App

Uma aplicação de chat com consultores especializados, construída com Next.js e integração com OpenRouter API.

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/leoaalvs-gmailcoms-projects/v0-whats-app-consultant-interface)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.app-black?style=for-the-badge)](https://v0.app/chat/projects/lgNJhQNl6Kv)

## Funcionalidades

- 🤖 **Múltiplos Modelos de IA**: Suporte a modelos gratuitos e pagos via OpenRouter
- 🔑 **Configuração de API Key**: Interface para inserir sua própria chave da OpenRouter API
- 👥 **Consultores Especializados**: Chat com diferentes especialistas (Contabilidade, Direito, Auditoria)
- 🎨 **Interface Moderna**: Design responsivo com Tailwind CSS e componentes Radix UI
- ⚙️ **Configurações Avançadas**: Teste de modelos, URLs customizadas e mais

## Como Usar

### 1. Configuração da API Key

1. Acesse as **Configurações** (ícone de engrenagem)
2. Insira sua **Chave da API OpenRouter** no campo apropriado
3. Selecione o **Modelo LLM** desejado
4. Teste a configuração usando a funcionalidade de teste
5. Salve as configurações

### 2. Obtendo uma Chave da OpenRouter API

1. Visite [OpenRouter.ai](https://openrouter.ai)
2. Crie uma conta ou faça login
3. Acesse a seção de API Keys
4. Gere uma nova chave (formato: `sk-or-v1-...`)
5. Copie e cole na aplicação

## Instalação e Execução

### Pré-requisitos
- Node.js 18+
- pnpm (recomendado) ou npm

### Passos para executar localmente

1. **Clone o repositório**
   ```bash
   git clone <url-do-repositorio>
   cd consultant-chat-app-main
   ```

2. **Instale as dependências**
   ```bash
   pnpm install
   ```

3. **Execute a aplicação**
   ```bash
   pnpm dev
   ```

4. **Acesse a aplicação**
   - Abra seu navegador em `http://localhost:3000` (ou a porta indicada no terminal)

## Modelos Disponíveis

### Modelos Gratuitos
- DeepSeek Chat V3
- DeepSeek R1
- Google Gemini Flash 1.5/2.0
- Meta Llama 3.1/3.3/4
- Mistral 7B Instruct
- Qwen 2.5 7B Instruct
- E muitos outros...

### Modelos Pagos (Requer API Key)
- Claude 3/3.5 (Haiku, Sonnet, Opus)
- GPT-4/4o/5
- OpenAI o1
- Gemini Pro
- Llama 3.1 70B/405B
- Grok 2
- E muitos outros...

## Tecnologias Utilizadas

- **Framework**: Next.js 15
- **UI**: Tailwind CSS + Radix UI
- **Linguagem**: TypeScript
- **API**: OpenRouter
- **Gerenciador de Pacotes**: pnpm

## Deployment

Your project is live at:

**[https://vercel.com/leoaalvs-gmailcoms-projects/v0-whats-app-consultant-interface](https://vercel.com/leoaalvs-gmailcoms-projects/v0-whats-app-consultant-interface)**

## Solução de Problemas

### ❌ Erro "Chave da API inválida"
- **Causa**: Chave da OpenRouter incorreta ou inativa
- **Solução**:
  1. Verifique se a chave começa com `sk-or-v1-`
  2. Confirme que a chave está ativa em [openrouter.ai](https://openrouter.ai)
  3. Verifique se há créditos suficientes na conta

### ❌ Erro "User not found"
- **Causa**: Chave da API não existe ou foi revogada
- **Solução**: Gere uma nova chave em [openrouter.ai](https://openrouter.ai)

### ❌ Erro "Créditos insuficientes"
- **Causa**: Saldo insuficiente na conta OpenRouter
- **Solução**: Adicione créditos à sua conta OpenRouter

### ❌ Erro "Limite de requisições excedido"
- **Causa**: Muitas requisições em pouco tempo
- **Solução**: Aguarde alguns minutos antes de tentar novamente

### 💡 Dicas
- Use modelos gratuitos para testes iniciais
- Monitore o uso de créditos na sua conta OpenRouter
- Mantenha sua chave da API segura e não a compartilhe

## Desenvolvimento

Continue building your app on:

**[https://v0.app/chat/projects/lgNJhQNl6Kv](https://v0.app/chat/projects/lgNJhQNl6Kv)**
